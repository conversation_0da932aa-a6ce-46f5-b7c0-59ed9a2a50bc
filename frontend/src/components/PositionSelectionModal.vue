<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative glass-card p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-semibold text-white">选择岗位</h3>
        <button @click="$emit('close')" class="text-white/70 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- 城市筛选提示 -->
      <div v-if="selectedCities.length === 0" class="mb-6 p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
          <span class="text-yellow-300">请先选择城市，岗位将根据所选城市进行筛选</span>
        </div>
      </div>

      <!-- 城市筛选器 -->
      <div v-if="selectedCities.length > 0" class="mb-6">
        <h4 class="text-lg font-semibold text-white mb-3">筛选城市</h4>
        <div class="flex flex-wrap gap-2">
          <button
            @click="filterCity = ''"
            class="px-3 py-1 rounded-lg text-sm transition-all"
            :class="filterCity === '' 
              ? 'btn-gemini' 
              : 'glass-card text-white/70 hover:text-white hover:bg-white/10'"
          >
            全部城市
          </button>
          <button
            v-for="city in selectedCities"
            :key="city.id"
            @click="filterCity = city.city_name"
            class="px-3 py-1 rounded-lg text-sm transition-all"
            :class="filterCity === city.city_name 
              ? 'btn-gemini' 
              : 'glass-card text-white/70 hover:text-white hover:bg-white/10'"
          >
            {{ city.city_name }}
          </button>
        </div>
      </div>
      
      <!-- 搜索框 -->
      <div class="mb-6">
        <div class="relative">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索岗位名称..."
            class="input-gemini pl-10 pr-4 py-2 w-full"
          />
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
        </div>
      </div>

      <!-- 已选择的岗位 -->
      <div v-if="selectedPositions.length > 0" class="mb-6">
        <h4 class="text-lg font-semibold text-white mb-3">已选择 ({{ selectedPositions.length }})</h4>
        <div class="flex flex-wrap gap-2">
          <div
            v-for="position in selectedPositions"
            :key="`${position.position_name}-${position.city_name}`"
            class="flex items-center bg-green-500/20 border border-green-500/30 rounded-lg px-3 py-2"
          >
            <span class="text-green-300 mr-2">{{ position.position_name }} ({{ position.city_name }})</span>
            <button
              @click="removePosition(position)"
              class="text-green-300 hover:text-white"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 岗位列表 -->
      <div class="mb-6">
        <h4 class="text-lg font-semibold text-white mb-3">可选岗位</h4>
        <div v-if="filteredPositions.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto">
          <div
            v-for="position in filteredPositions"
            :key="`${position.position_name}-${position.city_name}`"
            @click="togglePosition(position)"
            class="glass border border-white/10 rounded-lg p-4 cursor-pointer transition-all duration-300 hover:bg-white/10"
            :class="{ 'border-green-500 bg-green-500/20': isSelected(position) }"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <h5 class="font-semibold text-white">{{ position.position_name }}</h5>
                <div class="text-sm text-white/70 mt-1">
                  <div>城市: {{ position.city_name }}</div>
                  <div v-if="calculationType === 'monthly'">
                    月薪: ¥{{ position.monthly_salary?.toFixed(2) || '0.00' }}
                  </div>
                  <div v-else>
                    日薪: ¥{{ position.daily_salary?.toFixed(2) || '0.00' }}
                  </div>
                </div>
              </div>
              <div class="ml-4">
                <div
                  class="w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all"
                  :class="isSelected(position) 
                    ? 'border-green-500 bg-green-500' 
                    : 'border-white/30'"
                >
                  <svg v-if="isSelected(position)" class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8">
          <svg class="w-16 h-16 mx-auto text-white/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
          </svg>
          <p class="text-white/50">{{ selectedCities.length === 0 ? '请先选择城市' : '暂无匹配的岗位' }}</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-3">
        <button
          @click="$emit('close')"
          class="glass-card px-6 py-2 text-white hover:bg-white/10 transition-all"
        >
          取消
        </button>
        <button
          @click="confirmSelection"
          class="btn-gemini px-6 py-2"
          :disabled="selectedPositions.length === 0"
        >
          确认选择 ({{ selectedPositions.length }})
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuotationStore } from '../stores/quotation'

export default {
  name: 'PositionSelectionModal',
  props: {
    selectedCities: {
      type: Array,
      default: () => []
    },
    initialSelected: {
      type: Array,
      default: () => []
    },
    calculationType: {
      type: String,
      default: 'monthly'
    }
  },
  emits: ['close', 'confirm'],
  setup(props, { emit }) {
    const quotationStore = useQuotationStore()
    
    const searchQuery = ref('')
    const filterCity = ref('')
    const selectedPositions = ref([...props.initialSelected])
    
    const positions = computed(() => quotationStore.positions)
    
    // 根据选择的城市筛选岗位
    const availablePositions = computed(() => {
      if (props.selectedCities.length === 0) return []
      
      const cityNames = props.selectedCities.map(city => city.city_name)
      return positions.value.filter(position => 
        cityNames.includes(position.city_name)
      )
    })
    
    const filteredPositions = computed(() => {
      let filtered = availablePositions.value
      
      // 城市筛选
      if (filterCity.value) {
        filtered = filtered.filter(position => position.city_name === filterCity.value)
      }
      
      // 搜索筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(position => 
          position.position_name.toLowerCase().includes(query)
        )
      }
      
      return filtered
    })
    
    const isSelected = (position) => {
      return selectedPositions.value.some(selected => 
        selected.position_name === position.position_name && 
        selected.city_name === position.city_name
      )
    }
    
    const togglePosition = (position) => {
      if (isSelected(position)) {
        removePosition(position)
      } else {
        selectedPositions.value.push(position)
      }
    }
    
    const removePosition = (position) => {
      const index = selectedPositions.value.findIndex(selected => 
        selected.position_name === position.position_name && 
        selected.city_name === position.city_name
      )
      if (index > -1) {
        selectedPositions.value.splice(index, 1)
      }
    }
    
    const confirmSelection = () => {
      emit('confirm', selectedPositions.value)
      emit('close')
    }
    
    // 当选择的城市改变时，清除不在新城市列表中的岗位
    watch(() => props.selectedCities, (newCities) => {
      const cityNames = newCities.map(city => city.city_name)
      selectedPositions.value = selectedPositions.value.filter(position => 
        cityNames.includes(position.city_name)
      )
      filterCity.value = ''
    }, { deep: true })
    
    onMounted(async () => {
      if (positions.value.length === 0) {
        await quotationStore.fetchPositions()
      }
    })
    
    return {
      searchQuery,
      filterCity,
      selectedPositions,
      positions,
      availablePositions,
      filteredPositions,
      isSelected,
      togglePosition,
      removePosition,
      confirmSelection
    }
  }
}
</script>

<style scoped>
/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4285f4, #9c27b0);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9c27b0, #e91e63);
}
</style>
