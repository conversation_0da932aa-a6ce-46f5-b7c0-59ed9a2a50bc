#!/usr/bin/env python3
"""
测试文件上传功能
"""

import requests
import os

def test_upload():
    """测试上传功能"""
    # 创建一个测试文件
    test_file_path = "test_audio.txt"
    with open(test_file_path, "w") as f:
        f.write("这是一个测试文件，用于模拟音频上传")
    
    try:
        # 上传文件
        with open(test_file_path, "rb") as f:
            files = {"file": ("test_audio.mp3", f, "audio/mp3")}
            response = requests.post("http://localhost:8000/api/upload", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print("上传成功!")
            print(f"转录ID: {result['id']}")
            print(f"文件名: {result['filename']}")
            print(f"转录结果:\n{result['transcription']}")
        else:
            print(f"上传失败: {response.status_code}")
            print(response.text)
    
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"测试失败: {e}")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_history():
    """测试历史记录功能"""
    try:
        response = requests.get("http://localhost:8000/api/history")
        if response.status_code == 200:
            history = response.json()
            print(f"历史记录数量: {len(history)}")
            for item in history:
                print(f"- {item['filename']} ({item['created_at']})")
        else:
            print(f"获取历史记录失败: {response.status_code}")
    
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器")
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("API测试工具")
    print("=" * 50)
    
    print("1. 测试文件上传...")
    test_upload()
    
    print("\n2. 测试历史记录...")
    test_history()
    
    print("\n测试完成!")
