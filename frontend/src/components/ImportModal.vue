<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative glass-card p-6 w-full max-w-lg">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-white">导入Excel文件</h3>
        <button @click="$emit('close')" class="text-white/70 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      
      <!-- 格式说明 -->
      <div class="glass border border-blue-500/30 rounded-lg p-4 mb-6">
        <div class="flex items-start">
          <svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <div>
            <div class="text-sm font-medium text-blue-300 mb-2">Excel文件格式要求</div>
            <div class="text-xs text-white/70 space-y-1">
              <div>• 必须包含以下列：</div>
              <div class="ml-4">- 城市名称</div>
              <div class="ml-4">- 个人缴纳金额</div>
              <div class="ml-4">- 公司缴纳金额</div>
              <div>• 支持 .xlsx 和 .xls 格式</div>
              <div>• 第一行为表头，数据从第二行开始</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 文件上传区域 -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-white mb-2">
          选择Excel文件 <span class="text-red-400">*</span>
        </label>
        
        <div
          @drop="handleDrop"
          @dragover.prevent
          @dragenter.prevent
          class="border-2 border-dashed border-white/30 rounded-lg p-8 text-center transition-all duration-300"
          :class="{ 'border-blue-500 bg-blue-500/10': isDragging }"
        >
          <input
            ref="fileInput"
            type="file"
            accept=".xlsx,.xls"
            @change="handleFileSelect"
            class="hidden"
          />
          
          <div v-if="!selectedFile">
            <svg class="w-12 h-12 mx-auto text-white/50 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
            </svg>
            <p class="text-white/70 mb-2">拖拽文件到此处或</p>
            <button
              type="button"
              @click="$refs.fileInput.click()"
              class="btn-gemini px-4 py-2"
            >
              选择文件
            </button>
          </div>
          
          <div v-else class="flex items-center justify-center">
            <svg class="w-8 h-8 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <div>
              <div class="text-white font-medium">{{ selectedFile.name }}</div>
              <div class="text-white/50 text-sm">{{ formatFileSize(selectedFile.size) }}</div>
            </div>
            <button
              type="button"
              @click="clearFile"
              class="ml-4 text-white/50 hover:text-white"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          @click="$emit('close')"
          class="glass-card px-4 py-2 text-white hover:bg-white/10 transition-all"
        >
          取消
        </button>
        <button
          type="button"
          @click="handleImport"
          :disabled="loading || !selectedFile"
          class="btn-gemini px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="loading" class="animate-spin w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ loading ? '导入中...' : '开始导入' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'

export default {
  name: 'ImportModal',
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()
    
    const selectedFile = ref(null)
    const loading = ref(false)
    const isDragging = ref(false)
    
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        validateAndSetFile(file)
      }
    }
    
    const handleDrop = (event) => {
      event.preventDefault()
      isDragging.value = false
      
      const files = event.dataTransfer.files
      if (files.length > 0) {
        validateAndSetFile(files[0])
      }
    }
    
    const validateAndSetFile = (file) => {
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ]
      
      if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
        notificationStore.error('请选择Excel文件（.xlsx或.xls格式）')
        return
      }
      
      if (file.size > 10 * 1024 * 1024) { // 10MB
        notificationStore.error('文件大小不能超过10MB')
        return
      }
      
      selectedFile.value = file
    }
    
    const clearFile = () => {
      selectedFile.value = null
    }
    
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    const handleImport = async () => {
      if (!selectedFile.value) {
        notificationStore.warning('请选择要导入的文件')
        return
      }
      
      try {
        loading.value = true
        
        const result = await quotationStore.importCities(selectedFile.value)
        
        if (result.success) {
          emit('success')
          emit('close')
          notificationStore.success(result.message)
        } else {
          notificationStore.error(result.message || '导入失败')
        }
      } catch (error) {
        console.error('导入失败:', error)
        notificationStore.error('导入失败，请检查文件格式')
      } finally {
        loading.value = false
      }
    }
    
    return {
      selectedFile,
      loading,
      isDragging,
      handleFileSelect,
      handleDrop,
      clearFile,
      formatFileSize,
      handleImport
    }
  }
}
</script>

<style scoped>
.drag-over {
  border-color: #4285f4;
  background-color: rgba(66, 133, 244, 0.1);
}
</style>
