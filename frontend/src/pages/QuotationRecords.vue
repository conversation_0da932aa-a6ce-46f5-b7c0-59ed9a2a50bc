<template>
  <div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold gradient-text mb-4">报价记录</h1>
        <p class="text-xl text-white/70">查看和管理所有历史报价记录</p>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="glass-card p-6 text-center">
          <div class="text-3xl font-bold gradient-text mb-2">{{ totalRecords }}</div>
          <div class="text-white/70">总记录数</div>
        </div>
        <div class="glass-card p-6 text-center">
          <div class="text-3xl font-bold gradient-text mb-2">¥{{ totalAmount.toFixed(2) }}</div>
          <div class="text-white/70">总金额</div>
        </div>
        <div class="glass-card p-6 text-center">
          <div class="text-3xl font-bold gradient-text mb-2">{{ monthlyRecords }}</div>
          <div class="text-white/70">本月记录</div>
        </div>
        <div class="glass-card p-6 text-center">
          <div class="text-3xl font-bold gradient-text mb-2">¥{{ averageAmount.toFixed(2) }}</div>
          <div class="text-white/70">平均金额</div>
        </div>
      </div>

      <!-- 操作栏 -->
      <div class="glass-card p-4 mb-6">
        <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
          <div class="flex items-center space-x-4">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索商机名称..."
                class="input-gemini pl-10 pr-4 py-2 w-64"
              />
              <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
            <select v-model="filterType" class="select-gemini">
              <option value="">全部类型</option>
              <option value="monthly">按月计算</option>
              <option value="daily">按天计算</option>
            </select>
          </div>
          <button @click="exportAllRecords" class="btn-gemini">
            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            导出所有记录
          </button>
        </div>
      </div>

      <!-- 记录列表 -->
      <div class="glass-card p-6">
        <div v-if="filteredRecords.length" class="space-y-4">
          <div
            v-for="record in paginatedRecords"
            :key="record.id"
            class="glass border border-white/10 rounded-lg p-6 hover:bg-white/5 transition-all duration-300 cursor-pointer"
            @click="viewDetail(record)"
          >
            <div class="flex flex-col lg:flex-row lg:items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center mb-2">
                  <h3 class="text-xl font-semibold text-white mr-4">{{ record.opportunity_name }}</h3>
                  <span class="px-3 py-1 rounded-full text-xs font-medium"
                        :class="record.calculation_type === 'monthly' 
                          ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30' 
                          : 'bg-green-500/20 text-green-300 border border-green-500/30'">
                    {{ record.calculation_type === 'monthly' ? '按月计算' : '按天计算' }}
                  </span>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-white/70">
                  <div>
                    <span class="font-medium">选择城市：</span>
                    <span>{{ getSelectedCities(record).join(', ') }}</span>
                  </div>
                  <div>
                    <span class="font-medium">选择岗位：</span>
                    <span>{{ getSelectedPositions(record).join(', ') }}</span>
                  </div>
                  <div>
                    <span class="font-medium">工期：</span>
                    <span v-if="record.calculation_type === 'monthly'">
                      {{ record.work_duration_months }}个月
                    </span>
                    <span v-else>
                      {{ record.work_duration_days }}天 (考勤基数: {{ record.attendance_base }})
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-4 mt-4 lg:mt-0">
                <div class="text-right">
                  <div class="text-2xl font-bold gradient-text">¥{{ record.total_amount.toFixed(2) }}</div>
                  <div class="text-sm text-white/50">{{ formatDate(record.created_at) }}</div>
                </div>
                <div class="flex space-x-2">
                  <button
                    @click.stop="viewDetail(record)"
                    class="glass-card p-2 hover:bg-white/10 transition-all"
                    title="查看详情"
                  >
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                  </button>
                  <button
                    @click.stop="exportRecord(record.id)"
                    class="glass-card p-2 hover:bg-white/10 transition-all"
                    title="导出Excel"
                  >
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="text-center py-16">
          <svg class="w-24 h-24 mx-auto text-white/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <p class="text-white/50 text-lg mb-4">暂无报价记录</p>
          <router-link to="/quotation" class="btn-gemini">
            生成第一个报价
          </router-link>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="flex justify-center mt-8">
          <div class="flex space-x-2">
            <button
              v-for="page in visiblePages"
              :key="page"
              @click="currentPage = page"
              class="px-4 py-2 rounded-lg transition-all duration-300"
              :class="currentPage === page 
                ? 'btn-gemini' 
                : 'glass-card text-white/70 hover:text-white hover:bg-white/10'"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <QuotationDetailModal
      v-if="showDetailModal"
      :record="selectedRecord"
      @close="showDetailModal = false"
      @export="exportRecord"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'
import QuotationDetailModal from '../components/QuotationDetailModal.vue'

export default {
  name: 'QuotationRecords',
  components: {
    QuotationDetailModal
  },
  setup() {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()

    // 响应式数据
    const searchQuery = ref('')
    const filterType = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const showDetailModal = ref(false)
    const selectedRecord = ref(null)

    // 计算属性
    const records = computed(() => quotationStore.quotationRecords)

    const filteredRecords = computed(() => {
      let filtered = records.value

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(record =>
          record.opportunity_name.toLowerCase().includes(query)
        )
      }

      // 类型过滤
      if (filterType.value) {
        filtered = filtered.filter(record =>
          record.calculation_type === filterType.value
        )
      }

      return filtered
    })

    const paginatedRecords = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredRecords.value.slice(start, end)
    })

    const totalPages = computed(() =>
      Math.ceil(filteredRecords.value.length / pageSize.value)
    )

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...', total)
        } else if (current >= total - 3) {
          pages.push(1, '...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1, '...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...', total)
        }
      }

      return pages
    })

    const totalRecords = computed(() => records.value.length)
    const totalAmount = computed(() => quotationStore.totalAmount)

    const monthlyRecords = computed(() => {
      const now = new Date()
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()

      return records.value.filter(record => {
        const recordDate = new Date(record.created_at)
        return recordDate.getMonth() === currentMonth &&
               recordDate.getFullYear() === currentYear
      }).length
    })

    const averageAmount = computed(() => {
      return totalRecords.value > 0 ? totalAmount.value / totalRecords.value : 0
    })

    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const getSelectedCities = (record) => {
      try {
        return JSON.parse(record.selected_cities)
      } catch {
        return []
      }
    }

    const getSelectedPositions = (record) => {
      try {
        return JSON.parse(record.selected_positions)
      } catch {
        return []
      }
    }

    const viewDetail = async (record) => {
      try {
        const detail = await quotationStore.getQuotationDetail(record.id)
        selectedRecord.value = detail
        showDetailModal.value = true
      } catch (error) {
        console.error('获取报价详情失败:', error)
        notificationStore.error('获取报价详情失败')
      }
    }

    const exportRecord = (recordId) => {
      quotationStore.exportQuotation(recordId)
      notificationStore.success('报价导出成功！')
    }

    const exportAllRecords = () => {
      quotationStore.exportAllQuotations()
      notificationStore.success('所有记录导出成功！')
    }

    const loadData = async () => {
      try {
        await quotationStore.fetchQuotationRecords()
      } catch (error) {
        console.error('加载数据失败:', error)
        notificationStore.error('加载数据失败')
      }
    }

    // 生命周期
    onMounted(() => {
      loadData()
    })

    return {
      searchQuery,
      filterType,
      currentPage,
      showDetailModal,
      selectedRecord,
      filteredRecords,
      paginatedRecords,
      totalPages,
      visiblePages,
      totalRecords,
      totalAmount,
      monthlyRecords,
      averageAmount,
      formatDate,
      getSelectedCities,
      getSelectedPositions,
      viewDetail,
      exportRecord,
      exportAllRecords
    }
  }
}
</script>

<style scoped>
/* 记录卡片悬停效果 */
.record-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 搜索框样式 */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
}

/* 分页按钮动画 */
.pagination-btn {
  transition: all 0.3s ease;
}

.pagination-btn:hover {
  transform: translateY(-1px);
}
</style>
