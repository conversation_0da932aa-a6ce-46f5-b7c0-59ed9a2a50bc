from sqlalchemy.orm import Session
from typing import List, Dict, Any, AsyncGenerator
from ..models.database import MatchResult, Job, Resume
from ..models.schemas import MatchResultCreate
from ..agents.resume_matcher import ResumeMatcherAgent
import markdown
from docx import Document
from docx.shared import Inches
import io
from .pdf_export_service import PDFExportService


class MatchService:
    """匹配服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.matcher_agent = ResumeMatcherAgent()
    
    def create_match_result(self, match_data: MatchResultCreate) -> MatchResult:
        """创建匹配结果记录"""
        db_match = MatchResult(
            job_id=match_data.job_id,
            resume_id=match_data.resume_id,
            match_score=match_data.match_score,
            analysis_result=match_data.analysis_result
        )
        self.db.add(db_match)
        self.db.commit()
        self.db.refresh(db_match)
        return db_match
    
    def get_match_results(
        self, 
        job_id: int = None, 
        resume_id: int = None
    ) -> List[MatchResult]:
        """获取匹配结果"""
        query = self.db.query(MatchResult)
        
        if job_id:
            query = query.filter(MatchResult.job_id == job_id)
        if resume_id:
            query = query.filter(MatchResult.resume_id == resume_id)
        
        return query.all()
    
    async def analyze_matches(
        self, 
        job_id: int, 
        resume_ids: List[int]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """分析简历匹配度（流式输出）"""
        
        # 获取岗位信息
        job = self.db.query(Job).filter(Job.id == job_id).first()
        if not job:
            yield {"type": "error", "message": "岗位不存在"}
            return
        
        # 获取简历信息
        resumes = self.db.query(Resume).filter(Resume.id.in_(resume_ids)).all()
        if not resumes:
            yield {"type": "error", "message": "未找到指定的简历"}
            return
        
        # 准备简历数据
        resume_data = []
        for resume in resumes:
            resume_data.append({
                "id": resume.id,
                "filename": resume.original_filename,
                "text": resume.extracted_text or ""
            })
        
        # 使用代理进行批量分析
        async for result in self.matcher_agent.batch_analyze(
            job_description=job.description,
            job_requirements=job.requirements,
            resumes=resume_data,
            job_title=job.title
        ):
            # 如果是分析结果，保存到数据库
            if result.get("type") == "result" and result.get("status") == "success":
                match_data = MatchResultCreate(
                    job_id=job_id,
                    resume_id=result.get("resume_id"),
                    match_score=result.get("match_score"),
                    analysis_result=result.get("analysis_result")
                )
                self.create_match_result(match_data)
            
            yield result
    
    def export_to_word(self, match_results: List[MatchResult]) -> bytes:
        """导出匹配结果为Word文档"""
        doc = Document()
        
        # 添加标题
        title = doc.add_heading('简历匹配度分析报告', 0)
        
        for i, match in enumerate(match_results, 1):
            # 获取相关信息
            job = self.db.query(Job).filter(Job.id == match.job_id).first()
            resume = self.db.query(Resume).filter(Resume.id == match.resume_id).first()
            
            # 添加分节符
            if i > 1:
                doc.add_page_break()
            
            # 添加基本信息
            doc.add_heading(f'匹配结果 {i}', level=1)
            
            info_table = doc.add_table(rows=4, cols=2)
            info_table.style = 'Table Grid'
            
            info_table.cell(0, 0).text = '岗位标题'
            info_table.cell(0, 1).text = job.title if job else '未知'
            
            info_table.cell(1, 0).text = '简历文件'
            info_table.cell(1, 1).text = resume.original_filename if resume else '未知'
            
            info_table.cell(2, 0).text = '匹配度评分'
            info_table.cell(2, 1).text = f'{match.match_score:.1f}/100' if match.match_score else '未评分'
            
            info_table.cell(3, 0).text = '分析时间'
            info_table.cell(3, 1).text = match.created_at.strftime('%Y-%m-%d %H:%M:%S')
            
            # 添加详细的三维度分析结果
            self._add_detailed_analysis_to_doc(doc, match.analysis_result or "无分析结果")
        
        # 保存到内存
        doc_io = io.BytesIO()
        doc.save(doc_io)
        doc_io.seek(0)
        
        return doc_io.getvalue()

    def _add_detailed_analysis_to_doc(self, doc: Document, analysis_text: str):
        """将详细分析结果添加到Word文档"""
        import re

        # 分割分析结果和评审意见
        parts = analysis_text.split('---')
        analysis_part = parts[0] if parts else analysis_text
        review_part = parts[1] if len(parts) > 1 else ""

        # 添加分析结果部分
        doc.add_heading('匹配度分析', level=2)
        self._format_analysis_section(doc, analysis_part)

        # 如果有评审意见，添加评审部分
        if review_part.strip():
            doc.add_heading('专家评审意见', level=2)
            self._format_review_section(doc, review_part)

    def _format_analysis_section(self, doc: Document, analysis_text: str):
        """格式化分析部分"""
        import re

        # 提取三维度评分
        dimensions = self._extract_dimension_scores(analysis_text)

        if dimensions:
            # 创建评分表格
            score_table = doc.add_table(rows=4, cols=2)
            score_table.style = 'Table Grid'

            score_table.cell(0, 0).text = '评分维度'
            score_table.cell(0, 1).text = '得分'

            score_table.cell(1, 0).text = '技能匹配度'
            score_table.cell(1, 1).text = f"{dimensions.get('skill', 0)}/50"

            score_table.cell(2, 0).text = '工作经验匹配度'
            score_table.cell(2, 1).text = f"{dimensions.get('experience', 0)}/30"

            score_table.cell(3, 0).text = '教育背景匹配度'
            score_table.cell(3, 1).text = f"{dimensions.get('education', 0)}/20"

        # 格式化详细分析内容
        self._format_markdown_content(doc, analysis_text)

    def _format_review_section(self, doc: Document, review_text: str):
        """格式化评审部分"""
        self._format_markdown_content(doc, review_text)

    def _extract_dimension_scores(self, text: str) -> Dict[str, int]:
        """提取三维度评分"""
        import re

        dimensions = {}

        # 提取技能匹配度 - 支持新格式
        skill_patterns = [
            r'技能匹配度[^(]*\((\d+)/50\)',
            r'技能匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/50',
            r'\*\*评分\*\*:\s*(\d+)/50'
        ]

        for pattern in skill_patterns:
            skill_match = re.search(pattern, text)
            if skill_match:
                dimensions['skill'] = int(skill_match.group(1))
                break

        # 提取工作经验匹配度 - 支持新格式
        exp_patterns = [
            r'工作经验匹配度[^(]*\((\d+)/30\)',
            r'工作经验匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/30'
        ]

        for pattern in exp_patterns:
            exp_match = re.search(pattern, text)
            if exp_match:
                dimensions['experience'] = int(exp_match.group(1))
                break

        # 提取教育背景匹配度 - 支持新格式
        edu_patterns = [
            r'教育背景匹配度[^(]*\((\d+)/20\)',
            r'教育背景匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/20'
        ]

        for pattern in edu_patterns:
            edu_match = re.search(pattern, text)
            if edu_match:
                dimensions['education'] = int(edu_match.group(1))
                break

        return dimensions

    def _format_markdown_content(self, doc: Document, content: str):
        """格式化Markdown内容到Word文档"""
        import re

        lines = content.split('\n')
        current_list_level = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 处理标题
            if line.startswith('####'):
                doc.add_heading(line[4:].strip(), level=4)
            elif line.startswith('###'):
                doc.add_heading(line[3:].strip(), level=3)
            elif line.startswith('##'):
                doc.add_heading(line[2:].strip(), level=2)
            elif line.startswith('#'):
                doc.add_heading(line[1:].strip(), level=1)

            # 处理列表
            elif line.startswith('- **') or line.startswith('* **'):
                # 粗体列表项
                para = doc.add_paragraph()
                para.style = 'List Bullet'
                bold_match = re.match(r'[-*]\s*\*\*(.*?)\*\*:\s*(.*)', line)
                if bold_match:
                    para.add_run(bold_match.group(1) + ': ').bold = True
                    para.add_run(bold_match.group(2))
                else:
                    para.add_run(line[2:])

            elif line.startswith('- ') or line.startswith('* '):
                # 普通列表项
                para = doc.add_paragraph(line[2:])
                para.style = 'List Bullet'

            elif line.startswith(('1. ', '2. ', '3. ', '4. ')):
                # 数字列表
                para = doc.add_paragraph(line[3:])
                para.style = 'List Number'

            # 处理普通段落
            else:
                # 处理粗体和斜体
                para = doc.add_paragraph()
                self._add_formatted_text(para, line)

    def _add_formatted_text(self, paragraph, text: str):
        """添加格式化文本到段落"""
        import re

        # 处理粗体 **text**
        parts = re.split(r'(\*\*.*?\*\*)', text)
        for part in parts:
            if part.startswith('**') and part.endswith('**'):
                paragraph.add_run(part[2:-2]).bold = True
            else:
                paragraph.add_run(part)

    def get_match_summary(self, job_id: int) -> Dict[str, Any]:
        """获取匹配结果摘要"""
        matches = self.get_match_results(job_id=job_id)
        
        if not matches:
            return {
                "total_matches": 0,
                "average_score": 0,
                "highest_score": 0,
                "lowest_score": 0
            }
        
        scores = [m.match_score for m in matches if m.match_score is not None]
        
        return {
            "total_matches": len(matches),
            "average_score": sum(scores) / len(scores) if scores else 0,
            "highest_score": max(scores) if scores else 0,
            "lowest_score": min(scores) if scores else 0
        }
