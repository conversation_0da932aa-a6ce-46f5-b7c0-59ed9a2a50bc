#!/usr/bin/env python3
"""
测试报价金额计算修复的脚本
"""
import requests
import json

def test_external_price_calculation():
    """测试报价金额计算修复"""
    base_url = "http://localhost:5001"
    
    print("🧮 测试报价金额计算修复")
    print("=" * 50)
    
    # 1. 测试按月计算的报价
    print("\n1. 测试按月计算的报价")
    
    monthly_quotation = {
        "opportunity_name": "按月报价测试",
        "calculation_type": "monthly",
        "work_duration_months": 2,
        "quotation_coefficient": 1.0,
        "selected_cities": ["北京"],
        "selected_positions": [
            "软件开发工程师@北京@1"
        ],
        "external_prices": [50000]  # 用户输入的总报价金额（2个月的总价）
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate_quotation",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(monthly_quotation)
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 按月报价生成成功")
                print(f"   - 报价ID: {result.get('quotation_id')}")
                print(f"   - 总金额: ¥{result.get('total_amount'):.2f}")
                
                details = result.get('details', [])
                if details:
                    detail = details[0]
                    print(f"   - 明细验证:")
                    print(f"     工期: {detail.get('work_duration_months')} 个月")
                    print(f"     人数: {detail.get('count')} 人")
                    print(f"     成本: ¥{detail.get('subtotal'):.2f}")
                    print(f"     报价: ¥{detail.get('external_price'):.2f}")
                    print(f"     预期: ¥50000.00")
                    
                    if detail.get('external_price') == 50000:
                        print(f"     ✅ 报价金额正确（用户输入的总价）")
                    else:
                        print(f"     ❌ 报价金额错误")
                        
                monthly_quotation_id = result.get('quotation_id')
            else:
                print(f"❌ 按月报价生成失败: {result.get('message')}")
                monthly_quotation_id = None
        else:
            print(f"❌ 按月报价请求失败: {response.status_code}")
            monthly_quotation_id = None
    except Exception as e:
        print(f"❌ 按月报价异常: {e}")
        monthly_quotation_id = None
    
    # 2. 测试按天计算的报价
    print("\n2. 测试按天计算的报价")
    
    daily_quotation = {
        "opportunity_name": "按天报价测试",
        "calculation_type": "daily",
        "work_duration_days": 30,
        "quotation_coefficient": 1.0,
        "selected_cities": ["上海"],
        "selected_positions": [
            "UI设计师@上海@1"
        ],
        "external_prices": [25000]  # 用户输入的总报价金额（30天的总价）
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate_quotation",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(daily_quotation)
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 按天报价生成成功")
                print(f"   - 报价ID: {result.get('quotation_id')}")
                print(f"   - 总金额: ¥{result.get('total_amount'):.2f}")
                
                details = result.get('details', [])
                if details:
                    detail = details[0]
                    print(f"   - 明细验证:")
                    print(f"     工期: {detail.get('work_duration_days')} 天")
                    print(f"     人数: {detail.get('count')} 人")
                    print(f"     成本: ¥{detail.get('subtotal'):.2f}")
                    print(f"     报价: ¥{detail.get('external_price'):.2f}")
                    print(f"     预期: ¥25000.00")
                    
                    if detail.get('external_price') == 25000:
                        print(f"     ✅ 报价金额正确（用户输入的总价）")
                    else:
                        print(f"     ❌ 报价金额错误")
                        
                daily_quotation_id = result.get('quotation_id')
            else:
                print(f"❌ 按天报价生成失败: {result.get('message')}")
                daily_quotation_id = None
        else:
            print(f"❌ 按天报价请求失败: {response.status_code}")
            daily_quotation_id = None
    except Exception as e:
        print(f"❌ 按天报价异常: {e}")
        daily_quotation_id = None
    
    return monthly_quotation_id, daily_quotation_id

def test_quotation_details(monthly_id, daily_id):
    """测试报价详情显示"""
    base_url = "http://localhost:5001"
    
    print("\n3. 测试报价详情显示")
    
    for quotation_id, name in [(monthly_id, "按月报价"), (daily_id, "按天报价")]:
        if not quotation_id:
            print(f"   ⚠️ 跳过 {name} 详情测试（无有效ID）")
            continue
            
        try:
            response = requests.get(f"{base_url}/api/quotation_detail/{quotation_id}")
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ {name} 详情获取成功")
                
                record = result.get('record', {})
                details = result.get('details', [])
                
                print(f"     - 记录总金额: ¥{record.get('total_amount', 0):.2f}")
                
                if details:
                    detail = details[0]
                    print(f"     - 明细报价: ¥{detail.get('external_price', 0):.2f}")
                    print(f"     - 明细成本: ¥{detail.get('subtotal', 0):.2f}")
                    
                    if detail.get('external_price') == record.get('total_amount'):
                        print(f"     ✅ 详情页面报价金额正确")
                    else:
                        print(f"     ❌ 详情页面报价金额不一致")
            else:
                print(f"   ❌ {name} 详情获取失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name} 详情测试异常: {e}")

def test_multiple_positions():
    """测试多个岗位的报价"""
    base_url = "http://localhost:5001"
    
    print("\n4. 测试多个岗位的报价")
    
    multi_quotation = {
        "opportunity_name": "多岗位报价测试",
        "calculation_type": "monthly",
        "work_duration_months": 1,
        "quotation_coefficient": 1.0,
        "selected_cities": ["北京", "上海"],
        "selected_positions": [
            "软件开发工程师@北京@2",  # 2个人
            "UI设计师@上海@1"         # 1个人
        ],
        "external_prices": [40000, 15000]  # 每个岗位的总报价
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate_quotation",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(multi_quotation)
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 多岗位报价生成成功")
                print(f"   - 报价ID: {result.get('quotation_id')}")
                print(f"   - 总金额: ¥{result.get('total_amount'):.2f}")
                print(f"   - 预期总金额: ¥55000.00")
                
                if result.get('total_amount') == 55000:
                    print(f"   ✅ 总金额计算正确")
                else:
                    print(f"   ❌ 总金额计算错误")
                
                details = result.get('details', [])
                print(f"   - 明细验证:")
                for i, detail in enumerate(details):
                    expected_price = multi_quotation["external_prices"][i]
                    actual_price = detail.get('external_price')
                    print(f"     岗位 {i+1}: {detail['position_name']}")
                    print(f"       人数: {detail.get('count')} 人")
                    print(f"       报价: ¥{actual_price:.2f}")
                    print(f"       预期: ¥{expected_price:.2f}")
                    
                    if actual_price == expected_price:
                        print(f"       ✅ 报价金额正确")
                    else:
                        print(f"       ❌ 报价金额错误")
            else:
                print(f"❌ 多岗位报价生成失败: {result.get('message')}")
        else:
            print(f"❌ 多岗位报价请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 多岗位报价异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试报价金额计算修复...")
    
    monthly_id, daily_id = test_external_price_calculation()
    test_quotation_details(monthly_id, daily_id)
    test_multiple_positions()
    
    print("\n" + "=" * 50)
    print("✨ 报价金额计算修复测试完成！")
    
    print("\n📋 修复总结:")
    print("1. ✅ 理解用户输入逻辑")
    print("   - external_prices 是用户输入的总报价金额")
    print("   - 已经包含了人数和工期的考虑")
    print("   - 不需要后端再次计算")
    
    print("\n2. ✅ 后端处理逻辑")
    print("   - 直接使用用户输入的 external_price")
    print("   - 不再乘以人数和工期")
    print("   - 如果没有输入则使用成本")
    
    print("\n3. ✅ 数据流程正确")
    print("   - 前端：用户输入总报价金额")
    print("   - 后端：直接保存用户输入")
    print("   - 详情：正确显示保存的金额")
    
    print("\n🎯 技术实现:")
    print("- 后端: final_external_price = external_price if external_price else subtotal")
    print("- 前端: 用户在界面中输入每个岗位的总报价")
    print("- 数据库: 保存用户输入的完整报价金额")
    print("- 显示: 直接显示保存的报价金额")
    
    print("\n🌟 解决的问题:")
    print("- ✅ 报价金额不包含工期和人数")
    print("- ✅ 总金额计算错误")
    print("- ✅ 详情页面显示错误")
    print("- ✅ 前后端逻辑不一致")
    
    print("\n💡 用户使用说明:")
    print("1. 在报价生成页面选择岗位")
    print("2. 为每个岗位设置人数")
    print("3. 在'对外报价'字段输入该岗位的总报价金额")
    print("4. 总报价金额应该已经考虑了人数和工期")
    print("5. 系统会直接使用用户输入的金额")
    
    print("\n🌐 验证方法:")
    print("1. 访问: http://localhost:3000/quotation")
    print("2. 设置报价参数（工期、系数等）")
    print("3. 选择岗位并设置人数")
    print("4. 输入每个岗位的总报价金额")
    print("5. 生成报价并验证总金额")
    print("6. 查看报价详情验证显示正确")

if __name__ == '__main__':
    main()
