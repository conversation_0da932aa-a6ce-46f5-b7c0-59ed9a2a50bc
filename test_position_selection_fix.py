#!/usr/bin/env python3
"""
测试岗位选择页面修复的脚本
"""
import requests
import json

def test_position_api_fix():
    """测试岗位API修复"""
    base_url = "http://localhost:5001"
    
    print("🎯 测试岗位选择页面API修复")
    print("=" * 50)
    
    # 1. 测试原有分页API
    print("\n1. 测试原有分页API")
    try:
        response = requests.get(f"{base_url}/api/positions?page=1&page_size=10")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 分页API正常工作")
            print(f"   - 当前页: {data.get('page', 'N/A')}")
            print(f"   - 页大小: {data.get('page_size', 'N/A')}")
            print(f"   - 总数: {data.get('total', 'N/A')}")
            print(f"   - 当前页岗位数: {len(data.get('positions', []))}")
        else:
            print(f"❌ 分页API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 分页API异常: {e}")
        return False
    
    # 2. 测试新的获取所有岗位API
    print("\n2. 测试新的获取所有岗位API")
    try:
        response = requests.get(f"{base_url}/api/positions?all=true")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取所有岗位API正常工作")
            print(f"   - 总数: {data.get('total', 'N/A')}")
            print(f"   - 返回岗位数: {len(data.get('positions', []))}")
            
            positions = data.get('positions', [])
            if len(positions) == data.get('total', 0):
                print(f"✅ 返回了所有岗位数据（无分页限制）")
            else:
                print(f"❌ 返回的岗位数与总数不匹配")
                return False
            
            # 显示前5个岗位作为示例
            if positions:
                print(f"\n   前5个岗位示例:")
                for i, pos in enumerate(positions[:5]):
                    print(f"   {i+1}. {pos['position_name']} ({pos['city_name']}) - {pos.get('position_level', '未设置')}")
                    
        else:
            print(f"❌ 获取所有岗位API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取所有岗位API异常: {e}")
        return False
    
    # 3. 对比分页和全量API的差异
    print("\n3. 对比分页和全量API的差异")
    try:
        # 分页API
        paged_response = requests.get(f"{base_url}/api/positions?page=1&page_size=10")
        paged_data = paged_response.json()
        paged_count = len(paged_data.get('positions', []))
        
        # 全量API
        all_response = requests.get(f"{base_url}/api/positions?all=true")
        all_data = all_response.json()
        all_count = len(all_data.get('positions', []))
        
        print(f"   - 分页API返回: {paged_count} 个岗位")
        print(f"   - 全量API返回: {all_count} 个岗位")
        
        if all_count > paged_count:
            print(f"✅ 全量API返回更多岗位，修复成功")
            print(f"   - 差异: {all_count - paged_count} 个岗位")
        elif all_count == paged_count:
            print(f"⚠️ 两个API返回相同数量，可能岗位总数不超过分页大小")
        else:
            print(f"❌ 全量API返回更少岗位，可能有问题")
            
    except Exception as e:
        print(f"❌ 对比测试异常: {e}")
    
    return True

def test_frontend_integration():
    """测试前端集成"""
    print("\n4. 测试前端集成")
    
    try:
        response = requests.get("http://localhost:3000/quotation", timeout=10)
        if response.status_code == 200:
            print(f"✅ 前端页面正常访问")
        else:
            print(f"❌ 前端页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端页面访问异常: {e}")
    
    print(f"\n✅ 前端修复内容:")
    print(f"   - PositionSelectionModal 使用 fetchAllPositions()")
    print(f"   - 不再传递分页参数")
    print(f"   - 获取所有岗位数据")
    print(f"   - 确保岗位选择页面显示完整")

def provide_testing_guide():
    """提供测试指南"""
    print("\n5. 手动测试指南")
    
    print(f"\n🌐 页面测试步骤:")
    print(f"   1. 访问: http://localhost:3000/quotation")
    print(f"   2. 点击'选择岗位'按钮")
    print(f"   3. 检查岗位选择弹框中的岗位数量")
    print(f"   4. 验证是否显示了所有岗位")
    print(f"   5. 尝试搜索和筛选功能")
    
    print(f"\n🔍 验证要点:")
    print(f"   - 岗位选择弹框应显示所有岗位")
    print(f"   - 不应该有分页限制")
    print(f"   - 搜索和筛选功能正常")
    print(f"   - 岗位数据完整显示")
    
    print(f"\n🛠️ 技术验证:")
    print(f"   - 打开浏览器开发者工具")
    print(f"   - 查看Network标签")
    print(f"   - 点击选择岗位按钮")
    print(f"   - 检查API调用是否为 /api/positions?all=true")
    print(f"   - 验证返回的岗位数量")

def main():
    """主函数"""
    print("🚀 开始测试岗位选择页面修复...")
    
    success = test_position_api_fix()
    
    if success:
        test_frontend_integration()
        provide_testing_guide()
        
        print("\n" + "=" * 50)
        print("✨ 岗位选择页面API修复完成！")
        
        print("\n📋 修复总结:")
        print("1. ✅ 后端API增强")
        print("   - 添加 all=true 参数支持")
        print("   - 获取所有岗位，不分页")
        print("   - 保持原有分页功能")
        print("   - 支持筛选参数")
        
        print("\n2. ✅ 前端Store优化")
        print("   - 新增 fetchAllPositions() 方法")
        print("   - 专门用于获取所有岗位")
        print("   - 不传递分页参数")
        print("   - 保持原有 fetchPositions() 用于分页")
        
        print("\n3. ✅ 组件更新")
        print("   - PositionSelectionModal 使用 fetchAllPositions()")
        print("   - 确保显示所有岗位")
        print("   - 不受分页限制")
        print("   - 保持筛选和搜索功能")
        
        print("\n🎯 技术实现:")
        print("- 后端: 检查 all=true 参数，跳过分页")
        print("- 前端: 新增专用方法获取全量数据")
        print("- 兼容: 保持原有分页API不变")
        print("- 优化: 减少不必要的分页请求")
        
        print("\n🌟 解决的问题:")
        print("- ✅ 岗位选择页面显示不全")
        print("- ✅ 分页限制导致岗位缺失")
        print("- ✅ 用户无法看到所有可选岗位")
        print("- ✅ 影响报价生成的完整性")
        
        print("\n💡 使用建议:")
        print("- 岗位选择: 使用 fetchAllPositions()")
        print("- 数据管理: 使用 fetchPositions() 分页")
        print("- 搜索筛选: 两种方法都支持")
        print("- 性能考虑: 根据场景选择合适方法")
        
    else:
        print("\n❌ 修复验证失败，请检查错误信息")

if __name__ == '__main__':
    main()
