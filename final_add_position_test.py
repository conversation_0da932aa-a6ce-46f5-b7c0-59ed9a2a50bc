#!/usr/bin/env python3
"""
最终的添加岗位功能测试脚本
"""
import requests
import json

def test_complete_add_position():
    """完整测试添加岗位功能"""
    base_url = "http://localhost:5001"
    
    print("🔧 添加岗位保存报错问题修复验证")
    print("=" * 50)
    
    # 1. 测试前端后端连接
    print("\n1. 测试前后端连接")
    try:
        # 测试后端
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            print(f"✅ 后端服务器正常运行 (端口5001)")
        else:
            print(f"❌ 后端服务器异常: {response.status_code}")
            return False
        
        # 测试前端
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print(f"✅ 前端服务器正常运行 (端口3000)")
        else:
            print(f"❌ 前端服务器异常: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到前端服务器")
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
    
    # 2. 测试添加岗位完整流程
    print("\n2. 测试添加岗位完整流程")
    
    test_cases = [
        {
            "name": "完整数据测试",
            "data": {
                "position_name": "前端开发工程师",
                "city_name": "上海",
                "position_level": "高级",
                "monthly_salary": 18000.0,
                "daily_salary": 818.0
            },
            "should_succeed": True
        },
        {
            "name": "缺少岗位名称",
            "data": {
                "position_name": "",
                "city_name": "上海",
                "position_level": "高级",
                "monthly_salary": 18000.0,
                "daily_salary": 818.0
            },
            "should_succeed": False
        },
        {
            "name": "缺少城市",
            "data": {
                "position_name": "前端开发工程师",
                "city_name": "",
                "position_level": "高级",
                "monthly_salary": 18000.0,
                "daily_salary": 818.0
            },
            "should_succeed": False
        },
        {
            "name": "缺少岗位级别",
            "data": {
                "position_name": "前端开发工程师",
                "city_name": "上海",
                "position_level": "",
                "monthly_salary": 18000.0,
                "daily_salary": 818.0
            },
            "should_succeed": False
        }
    ]
    
    success_count = 0
    for i, test_case in enumerate(test_cases):
        try:
            response = requests.post(
                f"{base_url}/api/positions",
                headers={'Content-Type': 'application/json'},
                data=json.dumps(test_case["data"])
            )
            
            if response.status_code == 200:
                result = response.json()
                if test_case["should_succeed"]:
                    if result.get('success'):
                        print(f"✅ 测试 {i+1} ({test_case['name']}): 成功")
                        success_count += 1
                        # 清理测试数据
                        cleanup_test_data(base_url, test_case["data"])
                    else:
                        print(f"❌ 测试 {i+1} ({test_case['name']}): 应该成功但失败了 - {result.get('message')}")
                else:
                    if not result.get('success'):
                        print(f"✅ 测试 {i+1} ({test_case['name']}): 正确拒绝 - {result.get('message')}")
                        success_count += 1
                    else:
                        print(f"❌ 测试 {i+1} ({test_case['name']}): 应该失败但成功了")
            else:
                print(f"❌ 测试 {i+1} ({test_case['name']}): API请求失败 - {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试 {i+1} ({test_case['name']}) 异常: {e}")
    
    print(f"\n   测试结果: {success_count}/{len(test_cases)} 通过")
    
    # 3. 测试岗位级别数据
    print("\n3. 测试岗位级别数据")
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            data = response.json()
            positions = data.get('positions', [])
            
            level_stats = {}
            for pos in positions:
                level = pos.get('position_level', '未设置')
                level_stats[level] = level_stats.get(level, 0) + 1
            
            print(f"✅ 岗位级别统计:")
            for level, count in level_stats.items():
                print(f"   - {level}: {count} 个岗位")
                
            if '未设置' in level_stats:
                print(f"⚠️ 发现 {level_stats['未设置']} 个未设置级别的岗位")
            else:
                print(f"✅ 所有岗位都已设置级别")
                
        else:
            print(f"❌ 获取岗位数据失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 岗位级别测试异常: {e}")
    
    return success_count == len(test_cases)

def cleanup_test_data(base_url, position_data):
    """清理测试数据"""
    try:
        # 获取所有岗位，找到测试数据并删除
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            data = response.json()
            positions = data.get('positions', [])
            
            for pos in positions:
                if (pos['position_name'] == position_data['position_name'] and 
                    pos['city_name'] == position_data['city_name'] and
                    pos['position_level'] == position_data['position_level']):
                    
                    delete_response = requests.delete(f"{base_url}/api/positions/{pos['id']}")
                    if delete_response.status_code == 200:
                        print(f"   ✅ 清理测试数据: {pos['position_name']}")
                    break
    except Exception as e:
        print(f"   ⚠️ 清理测试数据异常: {e}")

def main():
    """主函数"""
    print("🚀 开始添加岗位保存报错问题修复验证...")
    
    success = test_complete_add_position()
    
    print("\n" + "=" * 50)
    if success:
        print("✨ 添加岗位保存报错问题修复完成！")
        print("\n📋 修复总结:")
        print("1. ✅ 岗位级别改为必填下拉选择")
        print("2. ✅ 后端API增加岗位级别验证")
        print("3. ✅ 前端表单验证包含岗位级别")
        print("4. ✅ 数据库初始化包含完整级别数据")
        
        print("\n🎯 修复内容:")
        print("- 前端：岗位级别改为下拉选择（必填）")
        print("- 后端：添加岗位级别不能为空的验证")
        print("- 数据：所有测试实例都包含级别信息")
        print("- 验证：完整的表单验证和错误处理")
        
        print("\n🌐 使用指南:")
        print("1. 访问: http://localhost:3000")
        print("2. 进入数据管理 → 岗位成本")
        print("3. 点击添加岗位按钮")
        print("4. 填写完整信息（岗位级别必选）:")
        print("   - 岗位名称：必填")
        print("   - 所属城市：必选")
        print("   - 岗位级别：必选（初级/中级/高级/专家）")
        print("   - 月工资：必填")
        print("   - 日工资：必填")
        print("5. 点击保存，成功后查看列表")
        
        print("\n💡 注意事项:")
        print("- 岗位级别现在是必填项")
        print("- 支持4种级别，每种有不同颜色标识")
        print("- 表单会实时验证所有必填字段")
        print("- 保存成功后自动刷新列表数据")
    else:
        print("❌ 添加岗位功能仍有问题，请检查错误信息")

if __name__ == '__main__':
    main()
