<template>
  <div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold gradient-text mb-4">智能报价生成</h1>
        <p class="text-xl text-white/70">AI驱动的精准成本计算</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
        <!-- 左侧参数设置 -->
        <div class="lg:col-span-2">
          <div class="glass-card p-6 sticky top-24">
            <h2 class="text-2xl font-semibold text-white mb-6 flex items-center">
              <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
              </svg>
              报价参数
            </h2>

            <form @submit.prevent="generateQuotation" class="space-y-6">
              <!-- 商机名称 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  商机名称 <span class="text-red-400">*</span>
                </label>
                <select
                  v-model="form.opportunityName"
                  class="select-gemini w-full"
                  required
                >
                  <option value="">请选择商机项目</option>
                  <option v-for="opportunity in opportunities" :key="opportunity.id" :value="opportunity.name">
                    {{ opportunity.name }}
                  </option>
                </select>
                <p class="text-xs text-white/50 mt-1">
                  如需添加新商机，请前往数据管理页面
                </p>
              </div>

              <!-- 计算类型 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  计算类型 <span class="text-red-400">*</span>
                </label>
                <div class="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    @click="form.calculationType = 'monthly'"
                    class="p-3 rounded-lg border transition-all duration-300"
                    :class="form.calculationType === 'monthly' 
                      ? 'border-blue-500 bg-blue-500/20 text-white' 
                      : 'border-white/20 bg-white/5 text-white/70 hover:bg-white/10'"
                  >
                    <div class="text-center">
                      <div class="text-lg font-semibold">按月</div>
                      <div class="text-xs">月度成本计算</div>
                    </div>
                  </button>
                  <button
                    type="button"
                    @click="form.calculationType = 'daily'"
                    class="p-3 rounded-lg border transition-all duration-300"
                    :class="form.calculationType === 'daily' 
                      ? 'border-blue-500 bg-blue-500/20 text-white' 
                      : 'border-white/20 bg-white/5 text-white/70 hover:bg-white/10'"
                  >
                    <div class="text-center">
                      <div class="text-lg font-semibold">按天</div>
                      <div class="text-xs">日度成本计算</div>
                    </div>
                  </button>
                </div>
              </div>

              <!-- 工期设置 -->
              <div v-if="form.calculationType === 'monthly'">
                <label class="block text-sm font-medium text-white mb-2">
                  工期（月） <span class="text-red-400">*</span>
                </label>
                <input
                  v-model="form.workDurationMonths"
                  type="number"
                  min="1"
                  class="input-gemini w-full"
                  placeholder="请输入工期月数"
                  required
                />
              </div>

              <div v-if="form.calculationType === 'daily'">
                <div>
                  <label class="block text-sm font-medium text-white mb-2">
                    工期（天） <span class="text-red-400">*</span>
                  </label>
                  <input
                    v-model="form.workDurationDays"
                    type="number"
                    min="1"
                    class="input-gemini w-full"
                    placeholder="请输入工期天数"
                    required
                  />
                </div>
              </div>

              <!-- 城市选择 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  选择社保城市 <span class="text-red-400">*</span>
                </label>
                <div class="glass border border-white/10 rounded-lg p-4">
                  <div class="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                    <label v-for="city in cities" :key="city.id" class="flex items-center space-x-2 cursor-pointer select-none">
                      <input
                        type="checkbox"
                        class="checkbox-gemini"
                        :value="city"
                        v-model="selectedCitiesData"
                        @change="selectedPositionsData = []"
                      />
                      <span class="text-white/80 text-sm">{{ city.city_name }}</span>
                    </label>
                  </div>
                  <div v-if="selectedCitiesData.length === 0" class="text-center py-2 text-white/50 text-xs">请至少选择一个社保城市</div>
                  <div v-else class="mt-2 text-xs text-blue-300">已选择 {{ selectedCitiesData.length }} 个城市</div>
                </div>
              </div>

              <!-- 岗位选择 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  选择岗位 <span class="text-red-400">*</span>
                </label>
                <div class="glass border border-white/10 rounded-lg p-4">
                  <div v-if="selectedPositionsData.length === 0" class="text-center py-4">
                    <svg class="w-12 h-12 mx-auto text-white/30 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <p class="text-white/50 text-sm mb-3">
                      {{ selectedCitiesData.length === 0 ? '请先选择城市' : '尚未选择岗位' }}
                    </p>
                    <button
                      @click="showPositionSelection = true"
                      :disabled="selectedCitiesData.length === 0"
                      class="btn-gemini px-4 py-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      选择岗位
                    </button>
                  </div>
                  <div v-else>
                    <div class="flex items-center justify-between mb-3">
                      <span class="text-white/70 text-sm">已选择 {{ selectedPositionsData.length }} 个岗位</span>
                      <button
                        @click="showPositionSelection = true"
                        class="text-green-400 hover:text-green-300 text-sm"
                      >
                        重新选择
                      </button>
                    </div>
                    <div class="flex flex-wrap gap-2">
                      <div
                        v-for="(position, idx) in selectedPositionsData"
                        :key="`${position.position_name}-${position.city_name}`"
                        class="flex items-center bg-green-500/20 border border-green-500/30 rounded-lg px-3 py-2"
                      >
                        <span class="text-green-300 text-sm mr-2">
                          {{ position.position_name }}
                          <template v-if="position.position_level">（{{ position.position_level }}）</template>
                          ({{ position.city_name }})
                        </span>
                        <input
                          type="number"
                          min="1"
                          v-model.number="position.count"
                          @input="updatePositionCount(idx, position.count)"
                          class="w-16 input-gemini text-center ml-2"
                          placeholder="人数"
                        />
                        <span class="text-green-200 text-xs ml-1">人</span>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          v-model.number="position.external_price"
                          class="w-24 input-gemini text-center ml-2"
                          placeholder="对外报价"
                        />
                        <span class="text-green-200 text-xs ml-1">元</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 生成按钮 -->
              <button
                type="submit"
                :disabled="loading || !canGenerate"
                class="btn-gemini w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="loading" class="animate-spin w-5 h-5 mr-2 inline" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                </svg>
                {{ loading ? '生成中...' : '生成报价' }}
              </button>
            </form>
          </div>
        </div>

        <!-- 右侧结果展示 -->
        <div class="lg:col-span-3">
          <div class="glass-card p-6">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                报价明细表
              </h2>
              <button
                v-if="quotationResult"
                @click="exportQuotation"
                class="btn-gemini px-4 py-2 text-sm"
              >
                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                导出Excel
              </button>
            </div>

            <!-- 结果展示区域 -->
            <div v-if="!quotationResult" class="text-center py-16">
              <svg class="w-24 h-24 mx-auto text-white/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <p class="text-white/50 text-lg">请设置报价参数并点击"生成报价"</p>
            </div>

            <!-- 报价结果 -->
            <div v-else class="space-y-6">
              <!-- 基本信息 -->
              <div class="glass border border-white/10 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-white mb-3">基本信息</h3>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-white/70">商机名称：</span>
                    <span class="text-white">{{ quotationResult && quotationResult.opportunity_name ? quotationResult.opportunity_name : '—' }}</span>
                  </div>
                  <div>
                    <span class="text-white/70">计算类型：</span>
                    <span class="text-white">{{ quotationResult && quotationResult.calculation_type ? (quotationResult.calculation_type === 'monthly' ? '按月' : '按天') : '—' }}</span>
                  </div>
                  <div v-if="quotationResult && quotationResult.calculation_type === 'monthly'">
                    <span class="text-white/70">工期：</span>
                    <span class="text-white">{{ quotationResult && quotationResult.work_duration_months ? quotationResult.work_duration_months + '个月' : '—' }}</span>
                  </div>
                  <div v-else>
                    <span class="text-white/70">工期：</span>
                    <span class="text-white">{{ quotationResult && quotationResult.work_duration_days ? quotationResult.work_duration_days + '天' : '—' }}</span>
                  </div>
                </div>
              </div>

              <!-- 明细表格 -->
              <div class="overflow-x-auto">
                <table class="min-w-full text-sm text-left">
                  <thead>
                    <tr class="border-b border-white/20">
                      <th class="text-left py-3 px-2 text-white/70">城市</th>
                      <th class="text-left py-3 px-2 text-white/70">岗位</th>
                      <th class="text-left py-3 px-2 text-white/70">级别</th>
                      <th class="text-left py-3 px-2 text-white/70">岗位人数</th>
                      <th class="text-left py-3 px-2 text-white/70" v-if="quotationResult.calculation_type === 'monthly'">月工资</th>
                      <th class="text-left py-3 px-2 text-white/70" v-else>日工资</th>
                      <th class="text-left py-3 px-2 text-white/70">公司社保</th>
                      <th class="text-left py-3 px-2 text-white/70">成本</th>
                      <th class="text-left py-3 px-2 text-white/70">报价金额</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="row in flatRows" :key="row.key"
                        :class="row.type === 'summary' ? 'bg-blue-500/10 border-b-2 border-blue-500/30' : 'border-b border-white/10 hover:bg-white/5 transition-colors'">
                      <template v-if="row.type === 'detail'">
                        <td class="py-3 px-2 text-white">{{ row.city_name }}</td>
                        <td class="py-3 px-2 text-white">{{ row.position_name }}</td>
                        <td class="py-3 px-2 text-white">{{ row.position_level || '—' }}</td>
                        <td class="py-3 px-2 text-white text-center">{{ row.count }}</td>
                        <td class="py-3 px-2 text-white" v-if="quotationResult.calculation_type === 'monthly'">
                          ¥{{ row.monthly_salary?.toFixed(2) || '0.00' }}
                        </td>
                        <td class="py-3 px-2 text-white" v-else>
                          ¥{{ row.daily_salary?.toFixed(2) || '0.00' }}
                        </td>
                        <td class="py-3 px-2 text-white text-center">
                          <span :title="`单月/单次：¥${row.company_insurance?.toFixed(2) || '0.00'}`">
                            <template v-if="quotationResult.calculation_type === 'monthly'">
                              ¥{{ (row.company_insurance * (row.work_duration_months || 1)).toFixed(2) }}
                            </template>
                            <template v-else>
                              ¥{{ (row.company_insurance * Math.round(row.work_duration_days / 30)).toFixed(2) }}
                            </template>
                          </span>
                        </td>
                        <td class="py-3 px-2 text-white font-semibold">¥{{ row.subtotal?.toFixed(2) || '0.00' }}</td>
                        <td class="py-3 px-2 text-white font-semibold">
                          ¥{{ (row.quoteAmount || 0).toFixed(2) }}
                        </td>
                      </template>
                      <template v-else>
                        <td colspan="10" class="py-2 px-2 text-blue-300 font-semibold text-left">
                          {{ row.cityName }} 合计：
                          <span class="ml-6">成本合计 <span class="font-bold">¥{{ row.total.toFixed(2) }}</span></span>
                          <span class="ml-6">报价合计 <span class="font-bold">¥{{ row.quoteTotal.toFixed(2) }}</span></span>
                        </td>
                      </template>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 城市选择弹框 -->
    <!-- <CitySelectionModal
      v-if="showCitySelection"
      :initial-selected="selectedCitiesData"
      @close="showCitySelection = false"
      @confirm="handleCitySelection"
    /> -->

    <!-- 岗位选择弹框 -->
    <PositionSelectionModal
      v-if="showPositionSelection"
      :selected-cities="selectedCitiesData"
      :initial-selected="selectedPositionsData"
      :calculation-type="form.calculationType"
      @close="showPositionSelection = false"
      @confirm="handlePositionSelection"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'
import CitySelectionModal from '../components/CitySelectionModal.vue'
import PositionSelectionModal from '../components/PositionSelectionModal.vue'

export default {
  name: 'QuotationGenerator',
  components: {
    // CitySelectionModal, // 注释掉弹框组件
    PositionSelectionModal
  },
  setup() {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()

    // 响应式数据
    const form = ref({
      opportunityName: '',
      calculationType: 'monthly',
      workDurationMonths: 1,
      workDurationDays: 30,
      quotationCoefficient: 1.00
    })

    const quotationResult = ref(null)
    const loading = ref(false)
    const showCitySelection = ref(false)
    const showPositionSelection = ref(false)
    const selectedCitiesData = ref([])
    const selectedPositionsData = ref([])

    // 计算属性
    const cities = computed(() => quotationStore.cities)
    const positions = computed(() => quotationStore.positions)
    const opportunities = computed(() => quotationStore.opportunities)

    const canGenerate = computed(() => {
      return form.value.opportunityName.trim() &&
             selectedCitiesData.value.length > 0 &&
             selectedPositionsData.value.length > 0 &&
             form.value.quotationCoefficient > 0 &&
             (form.value.calculationType === 'monthly' ?
               form.value.workDurationMonths > 0 :
               form.value.workDurationDays > 0)
    })

    // 替换 groupedDetails，新增 flatRows
    const flatRows = computed(() => {
      if (!quotationResult.value || !quotationResult.value.details) return [];
      const groups = {};
      // 建立岗位key到external_price的映射
      const posMap = {};
      selectedPositionsData.value.forEach(pos => {
        const key = `${pos.position_name}@${pos.city_name}`;
        posMap[key] = pos;
      });
      quotationResult.value.details.forEach(detail => {
        const cityName = detail.city_name;
        if (!groups[cityName]) {
          groups[cityName] = { details: [], total: 0 };
        }
        // 取回显时的external_price
        const key = `${detail.position_name}@${detail.city_name}`;
        const extPrice = posMap[key]?.external_price || 0;
        const count = posMap[key]?.count || detail.count || 1;
        const period = quotationResult.value.calculation_type === 'monthly'
          ? (Number(detail.work_duration_months) || 1)
          : (Number(detail.work_duration_days) || 1);
        const quoteAmount = Number(extPrice) * Number(count) * Number(period);
        groups[cityName].details.push({ ...detail, external_price: extPrice, count, quoteAmount });
        groups[cityName].total += detail.subtotal;
      });
      // 展平成一维数组
      const rows = [];
      Object.entries(groups).forEach(([cityName, group]) => {
        group.details.forEach((detail, idx) => {
          rows.push({ ...detail, type: 'detail', key: `${cityName}-${idx}` });
        });
        // 计算报价合计
        const quoteTotal = group.details.reduce((sum, detail) => sum + (Number(detail.quoteAmount) || 0), 0);
        rows.push({ type: 'summary', cityName, total: group.total, quoteTotal, key: `summary-${cityName}` });
      });
      return rows;
    });

    // 方法
    const generateQuotation = async () => {
      if (loading.value || !canGenerate.value) return;
      loading.value = true;
      try {
        // 构造 selected_positions，带上人数和对外报价
        const selected_positions = selectedPositionsData.value.map(pos => {
          // 兼容后端格式：岗位名@城市名@人数
          return `${pos.position_name}@${pos.city_name}@${pos.count || 1}`;
        });
        // 构造 external_prices，按顺序传递
        const external_prices = selectedPositionsData.value.map(pos => pos.external_price || 0);

        const payload = {
          opportunity_name: form.value.opportunityName,
          calculation_type: form.value.calculationType,
          work_duration_months: form.value.workDurationMonths,
          work_duration_days: form.value.workDurationDays,
          quotation_coefficient: form.value.quotationCoefficient,
          selected_cities: selectedCitiesData.value.map(c => c.city_name),
          selected_positions,
          external_prices
        };
        const res = await quotationStore.generateQuotation(payload);
        quotationResult.value = res;
      } catch (e) {
        notificationStore.notify({ type: 'error', message: '生成报价失败，请检查参数！' });
      } finally {
        loading.value = false;
      }
    }

    const exportQuotation = () => {
      // 实现导出Excel的逻辑
    }

    const handleCitySelection = (selectedCities) => {
      // 实现城市选择的逻辑
    }

    const handlePositionSelection = (positions) => {
      selectedPositionsData.value = positions.map(pos => ({ ...pos, count: 1 }))
    }

    const updatePositionCount = (index, count) => {
      // 实现岗位人数更新的逻辑
    }

    return {
      form,
      quotationResult,
      loading,
      showCitySelection,
      showPositionSelection,
      selectedCitiesData,
      selectedPositionsData,
      cities,
      positions,
      opportunities,
      canGenerate,
      flatRows,
      generateQuotation,
      exportQuotation,
      handleCitySelection,
      handlePositionSelection,
      updatePositionCount
    }
  }
}
</script>

<style scoped>
/* 你可以在这里添加自定义样式 */
</style>