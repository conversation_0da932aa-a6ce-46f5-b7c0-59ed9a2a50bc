from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import pandas as pd
import os
from io import BytesIO
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///quotation_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# 添加自定义过滤器
@app.template_filter('from_json')
def from_json_filter(value):
    """将JSON字符串转换为Python对象"""
    try:
        return json.loads(value)
    except:
        return []

# 数据库模型
class Opportunity(db.Model):
    """商机表"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class CitySocialInsurance(db.Model):
    """城市社保公积金表"""
    id = db.Column(db.Integer, primary_key=True)
    city_name = db.Column(db.String(100), nullable=False, unique=True)
    social_base = db.Column(db.Float, nullable=False)      # 社保基数（仅显示，不参与计算）
    company_amount = db.Column(db.Float, nullable=False)   # 公司缴纳金额
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class PositionCost(db.Model):
    """岗位成本表"""
    id = db.Column(db.Integer, primary_key=True)
    position_name = db.Column(db.String(100), nullable=False, unique=True)
    monthly_salary = db.Column(db.Float, nullable=False)  # 月工资
    daily_salary = db.Column(db.Float, nullable=False)    # 日工资
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class QuotationRecord(db.Model):
    """报价记录表"""
    id = db.Column(db.Integer, primary_key=True)
    opportunity_name = db.Column(db.String(200), nullable=False)
    selected_cities = db.Column(db.Text, nullable=False)  # JSON格式存储选中的城市
    selected_positions = db.Column(db.Text, nullable=False)  # JSON格式存储选中的岗位
    work_duration_months = db.Column(db.Integer, nullable=True)  # 工期（月）
    work_duration_days = db.Column(db.Integer, nullable=True)    # 工期（天）
    calculation_type = db.Column(db.String(10), nullable=False)  # 计算类型：monthly/daily
    quotation_coefficient = db.Column(db.Numeric(5, 2), nullable=True)  # 报价系数（支持两位小数）
    total_amount = db.Column(db.Float, nullable=False)  # 总金额
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关联报价明细
    details = db.relationship('QuotationDetail', backref='quotation', lazy=True, cascade='all, delete-orphan')

class QuotationDetail(db.Model):
    """报价明细表"""
    id = db.Column(db.Integer, primary_key=True)
    quotation_id = db.Column(db.Integer, db.ForeignKey('quotation_record.id'), nullable=False)
    city_name = db.Column(db.String(100), nullable=False)
    position_name = db.Column(db.String(100), nullable=False)
    monthly_salary = db.Column(db.Float, nullable=True)     # 月工资
    daily_salary = db.Column(db.Float, nullable=True)       # 日工资
    social_base = db.Column(db.Float, nullable=False)       # 社保基数（仅显示）
    company_insurance = db.Column(db.Float, nullable=False)
    work_duration_months = db.Column(db.Integer, nullable=True)  # 工期（月）
    work_duration_days = db.Column(db.Integer, nullable=True)    # 工期（天）
    calculation_type = db.Column(db.String(10), nullable=False)  # 计算类型
    quotation_coefficient = db.Column(db.Numeric(5, 2), nullable=True)  # 报价系数（支持两位小数）
    subtotal = db.Column(db.Float, nullable=False)  # 小计金额

# 路由定义
@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/opportunities')
def opportunities():
    """商机管理页面"""
    opportunities = Opportunity.query.all()
    return render_template('opportunities.html', opportunities=opportunities)

@app.route('/api/opportunities', methods=['GET'])
def get_opportunities():
    """获取所有商机"""
    opportunities = Opportunity.query.all()
    return jsonify([{
        'id': opp.id,
        'name': opp.name,
        'description': opp.description,
        'created_at': opp.created_at.isoformat()
    } for opp in opportunities])

@app.route('/api/cities', methods=['GET'])
def get_cities():
    """获取所有城市"""
    cities = CitySocialInsurance.query.all()
    return jsonify([{
        'id': city.id,
        'city_name': city.city_name,
        'social_base': city.social_base,
        'company_amount': city.company_amount,
        'created_at': city.created_at.isoformat(),
        'updated_at': city.updated_at.isoformat()
    } for city in cities])

@app.route('/api/positions', methods=['GET'])
def get_positions():
    """获取所有岗位"""
    positions = PositionCost.query.all()
    return jsonify([{
        'id': pos.id,
        'position_name': pos.position_name,
        'monthly_salary': pos.monthly_salary,
        'daily_salary': pos.daily_salary,
        'created_at': pos.created_at.isoformat(),
        'updated_at': pos.updated_at.isoformat()
    } for pos in positions])

@app.route('/api/quotation_records', methods=['GET'])
def get_quotation_records():
    """获取所有报价记录"""
    records = QuotationRecord.query.order_by(QuotationRecord.created_at.desc()).all()
    return jsonify([{
        'id': record.id,
        'opportunity_name': record.opportunity_name,
        'selected_cities': record.selected_cities,
        'selected_positions': record.selected_positions,
        'work_duration_months': record.work_duration_months,
        'work_duration_days': record.work_duration_days,
        'calculation_type': record.calculation_type,
        'quotation_coefficient': float(record.quotation_coefficient) if record.quotation_coefficient else None,
        'total_amount': record.total_amount,
        'created_at': record.created_at.isoformat()
    } for record in records])

@app.route('/api/opportunities', methods=['POST'])
def add_opportunity():
    """添加商机"""
    data = request.get_json()
    name = data.get('name', '').strip()
    description = data.get('description', '').strip()
    
    if not name:
        return jsonify({'success': False, 'message': '商机名称不能为空'})
    
    # 检查是否已存在
    existing = Opportunity.query.filter_by(name=name).first()
    if existing:
        return jsonify({'success': False, 'message': '商机名称已存在'})
    
    opportunity = Opportunity(name=name, description=description)
    db.session.add(opportunity)
    db.session.commit()
    
    return jsonify({'success': True, 'message': '商机添加成功'})

@app.route('/cities')
def cities():
    """城市社保公积金管理页面"""
    cities = CitySocialInsurance.query.all()
    return render_template('cities.html', cities=cities)

@app.route('/api/cities', methods=['POST'])
def add_city():
    """添加城市社保公积金数据"""
    data = request.get_json()
    city_name = data.get('city_name', '').strip()
    social_base = data.get('social_base', 0)
    company_amount = data.get('company_amount', 0)

    if not city_name:
        return jsonify({'success': False, 'message': '城市名称不能为空'})

    try:
        social_base = float(social_base)
        company_amount = float(company_amount)
    except ValueError:
        return jsonify({'success': False, 'message': '金额必须为数字'})

    # 检查是否已存在
    existing = CitySocialInsurance.query.filter_by(city_name=city_name).first()
    if existing:
        # 更新现有记录
        existing.social_base = social_base
        existing.company_amount = company_amount
        existing.updated_at = datetime.utcnow()
    else:
        # 创建新记录
        city = CitySocialInsurance(
            city_name=city_name,
            social_base=social_base,
            company_amount=company_amount
        )
        db.session.add(city)

    db.session.commit()
    return jsonify({'success': True, 'message': '城市数据保存成功'})

@app.route('/positions')
def positions():
    """岗位成本管理页面"""
    positions = PositionCost.query.all()
    return render_template('positions.html', positions=positions)

@app.route('/api/positions', methods=['POST'])
def add_position():
    """添加岗位成本数据"""
    data = request.get_json()
    position_name = data.get('position_name', '').strip()
    monthly_salary = data.get('monthly_salary', 0)
    daily_salary = data.get('daily_salary', 0)

    if not position_name:
        return jsonify({'success': False, 'message': '岗位名称不能为空'})

    try:
        monthly_salary = float(monthly_salary)
        daily_salary = float(daily_salary)
    except ValueError:
        return jsonify({'success': False, 'message': '工资必须为数字'})

    # 检查是否已存在
    existing = PositionCost.query.filter_by(position_name=position_name).first()
    if existing:
        # 更新现有记录
        existing.monthly_salary = monthly_salary
        existing.daily_salary = daily_salary
        existing.updated_at = datetime.utcnow()
    else:
        # 创建新记录
        position = PositionCost(
            position_name=position_name,
            monthly_salary=monthly_salary,
            daily_salary=daily_salary
        )
        db.session.add(position)

    db.session.commit()
    return jsonify({'success': True, 'message': '岗位数据保存成功'})

@app.route('/quotation')
def quotation():
    """报价生成页面"""
    opportunities = Opportunity.query.all()
    cities = CitySocialInsurance.query.all()
    positions = PositionCost.query.all()
    return render_template('quotation.html',
                         opportunities=opportunities,
                         cities=cities,
                         positions=positions)

@app.route('/api/generate_quotation', methods=['POST'])
def generate_quotation():
    """生成报价"""
    data = request.get_json()
    opportunity_name = data.get('opportunity_name', '').strip()
    selected_cities = data.get('selected_cities', [])
    selected_positions = data.get('selected_positions', [])
    calculation_type = data.get('calculation_type', 'monthly')  # monthly 或 daily
    work_duration_months = data.get('work_duration_months', 0)
    work_duration_days = data.get('work_duration_days', 0)
    quotation_coefficient = data.get('quotation_coefficient', 1.0)

    if not opportunity_name:
        return jsonify({'success': False, 'message': '请输入商机名称'})

    if not selected_cities:
        return jsonify({'success': False, 'message': '请选择至少一个城市'})

    if not selected_positions:
        return jsonify({'success': False, 'message': '请选择至少一个岗位'})

    # 验证工期参数
    if calculation_type == 'monthly':
        try:
            work_duration_months = int(work_duration_months)
            if work_duration_months <= 0:
                return jsonify({'success': False, 'message': '工期（月）必须大于0'})
        except ValueError:
            return jsonify({'success': False, 'message': '工期（月）必须为整数'})
    else:  # daily
        try:
            work_duration_days = int(work_duration_days)
            quotation_coefficient = round(float(quotation_coefficient), 2)  # 保留两位小数
            if work_duration_days <= 0:
                return jsonify({'success': False, 'message': '工期（天）必须大于0'})
            if quotation_coefficient <= 0:
                return jsonify({'success': False, 'message': '报价系数必须大于0'})
        except ValueError:
            return jsonify({'success': False, 'message': '工期和报价系数必须为数字'})

    # 获取城市和岗位数据
    cities_data = {city.city_name: city for city in CitySocialInsurance.query.filter(CitySocialInsurance.city_name.in_(selected_cities)).all()}
    positions_data = {pos.position_name: pos for pos in PositionCost.query.filter(PositionCost.position_name.in_(selected_positions)).all()}

    # 检查数据完整性
    missing_cities = [city for city in selected_cities if city not in cities_data]
    missing_positions = [pos for pos in selected_positions if pos not in positions_data]

    if missing_cities:
        return jsonify({'success': False, 'message': f'缺少城市数据: {", ".join(missing_cities)}'})

    if missing_positions:
        return jsonify({'success': False, 'message': f'缺少岗位数据: {", ".join(missing_positions)}'})

    # 生成报价明细
    quotation_details = []
    total_amount = 0

    for city_name in selected_cities:
        city_data = cities_data[city_name]
        for position_name in selected_positions:
            position_data = positions_data[position_name]

            if calculation_type == 'monthly':
                # 按月计算：(月工资 + 公司社保公积金) * 工期 * 报价系数
                base_cost = position_data.monthly_salary + city_data.company_amount
                subtotal = base_cost * work_duration_months * quotation_coefficient

                detail = {
                    'city_name': city_name,
                    'position_name': position_name,
                    'monthly_salary': position_data.monthly_salary,
                    'daily_salary': None,
                    'social_base': city_data.social_base,
                    'company_insurance': city_data.company_amount,
                    'work_duration_months': work_duration_months,
                    'work_duration_days': None,
                    'calculation_type': 'monthly',
                    'quotation_coefficient': quotation_coefficient,
                    'base_cost': base_cost,
                    'subtotal': subtotal
                }
            else:  # daily
                # 按天计算：(日工资 + 公司社保公积金) * 工期 * 报价系数
                base_cost = position_data.daily_salary + city_data.company_amount
                subtotal = base_cost * work_duration_days * quotation_coefficient

                detail = {
                    'city_name': city_name,
                    'position_name': position_name,
                    'monthly_salary': None,
                    'daily_salary': position_data.daily_salary,
                    'social_base': city_data.social_base,
                    'company_insurance': city_data.company_amount,
                    'work_duration_months': None,
                    'work_duration_days': work_duration_days,
                    'calculation_type': 'daily',
                    'quotation_coefficient': quotation_coefficient,
                    'base_cost': base_cost,
                    'subtotal': subtotal
                }

            total_amount += subtotal
            quotation_details.append(detail)

    # 保存报价记录
    quotation_record = QuotationRecord(
        opportunity_name=opportunity_name,
        selected_cities=json.dumps(selected_cities, ensure_ascii=False),
        selected_positions=json.dumps(selected_positions, ensure_ascii=False),
        work_duration_months=work_duration_months if calculation_type == 'monthly' else None,
        work_duration_days=work_duration_days if calculation_type == 'daily' else None,
        calculation_type=calculation_type,
        quotation_coefficient=quotation_coefficient,
        total_amount=total_amount
    )
    db.session.add(quotation_record)
    db.session.flush()  # 获取ID

    # 保存报价明细
    for detail in quotation_details:
        quotation_detail = QuotationDetail(
            quotation_id=quotation_record.id,
            city_name=detail['city_name'],
            position_name=detail['position_name'],
            monthly_salary=detail['monthly_salary'],
            daily_salary=detail['daily_salary'],
            social_base=detail['social_base'],
            company_insurance=detail['company_insurance'],
            work_duration_months=detail['work_duration_months'],
            work_duration_days=detail['work_duration_days'],
            calculation_type=detail['calculation_type'],
            quotation_coefficient=detail['quotation_coefficient'],
            subtotal=detail['subtotal']
        )
        db.session.add(quotation_detail)

    db.session.commit()

    return jsonify({
        'success': True,
        'message': '报价生成成功',
        'quotation_id': quotation_record.id,
        'details': quotation_details,
        'total_amount': total_amount
    })

@app.route('/quotation_records')
def quotation_records():
    """报价记录页面"""
    records = QuotationRecord.query.order_by(QuotationRecord.created_at.desc()).all()
    return render_template('quotation_records.html', records=records)

@app.route('/api/quotation_detail/<int:quotation_id>')
def get_quotation_detail(quotation_id):
    """获取报价详情"""
    record = QuotationRecord.query.get_or_404(quotation_id)
    details = QuotationDetail.query.filter_by(quotation_id=quotation_id).all()

    detail_list = []
    for detail in details:
        detail_list.append({
            'city_name': detail.city_name,
            'position_name': detail.position_name,
            'monthly_salary': detail.monthly_salary,
            'personal_insurance': detail.personal_insurance,
            'company_insurance': detail.company_insurance,
            'work_duration_months': detail.work_duration_months,
            'subtotal': detail.subtotal
        })

    return jsonify({
        'record': {
            'id': record.id,
            'opportunity_name': record.opportunity_name,
            'selected_cities': json.loads(record.selected_cities),
            'selected_positions': json.loads(record.selected_positions),
            'work_duration_months': record.work_duration_months,
            'total_amount': record.total_amount,
            'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
        },
        'details': detail_list
    })

@app.route('/api/export_quotation/<int:quotation_id>')
def export_quotation(quotation_id):
    """导出单个报价为Excel"""
    record = QuotationRecord.query.get_or_404(quotation_id)
    details = QuotationDetail.query.filter_by(quotation_id=quotation_id).all()

    # 创建Excel数据
    data = []
    for detail in details:
        data.append({
            '城市': detail.city_name,
            '岗位': detail.position_name,
            '月工资': detail.monthly_salary,
            '个人社保公积金': detail.personal_insurance,
            '公司社保公积金': detail.company_insurance,
            '工期(月)': detail.work_duration_months,
            '小计': detail.subtotal
        })

    df = pd.DataFrame(data)

    # 创建Excel文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='报价明细', index=False)

        # 添加汇总信息
        summary_data = {
            '商机名称': [record.opportunity_name],
            '总金额': [record.total_amount],
            '工期(月)': [record.work_duration_months],
            '生成时间': [record.created_at.strftime('%Y-%m-%d %H:%M:%S')]
        }
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='报价汇总', index=False)

    output.seek(0)

    filename = f"报价_{record.opportunity_name}_{record.created_at.strftime('%Y%m%d_%H%M%S')}.xlsx"

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )

@app.route('/api/export_all_quotations')
def export_all_quotations():
    """导出所有报价记录为Excel"""
    records = QuotationRecord.query.order_by(QuotationRecord.created_at.desc()).all()

    # 创建汇总数据
    summary_data = []
    for record in records:
        summary_data.append({
            'ID': record.id,
            '商机名称': record.opportunity_name,
            '选择城市': ', '.join(json.loads(record.selected_cities)),
            '选择岗位': ', '.join(json.loads(record.selected_positions)),
            '工期(月)': record.work_duration_months,
            '总金额': record.total_amount,
            '生成时间': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    df = pd.DataFrame(summary_data)

    # 创建Excel文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='报价记录汇总', index=False)

        # 为每个报价添加详细明细表
        for record in records[:10]:  # 限制前10个记录，避免文件过大
            details = QuotationDetail.query.filter_by(quotation_id=record.id).all()
            detail_data = []
            for detail in details:
                detail_data.append({
                    '城市': detail.city_name,
                    '岗位': detail.position_name,
                    '月工资': detail.monthly_salary,
                    '个人社保公积金': detail.personal_insurance,
                    '公司社保公积金': detail.company_insurance,
                    '工期(月)': detail.work_duration_months,
                    '小计': detail.subtotal
                })

            if detail_data:
                detail_df = pd.DataFrame(detail_data)
                sheet_name = f"报价{record.id}_{record.opportunity_name[:10]}"
                detail_df.to_excel(writer, sheet_name=sheet_name, index=False)

    output.seek(0)

    filename = f"所有报价记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )

@app.route('/api/import_cities', methods=['POST'])
def import_cities():
    """导入城市社保公积金数据"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '请选择文件'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '请选择文件'})

    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'success': False, 'message': '请上传Excel文件'})

    try:
        df = pd.read_excel(file)

        # 检查必需的列
        required_columns = ['城市名称', '个人缴纳金额', '公司缴纳金额']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            return jsonify({'success': False, 'message': f'缺少必需的列: {", ".join(missing_columns)}'})

        # 导入数据
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            city_name = str(row['城市名称']).strip()
            personal_amount = float(row['个人缴纳金额'])
            company_amount = float(row['公司缴纳金额'])

            if not city_name:
                continue

            existing = CitySocialInsurance.query.filter_by(city_name=city_name).first()
            if existing:
                existing.personal_amount = personal_amount
                existing.company_amount = company_amount
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                city = CitySocialInsurance(
                    city_name=city_name,
                    personal_amount=personal_amount,
                    company_amount=company_amount
                )
                db.session.add(city)
                imported_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'导入成功！新增 {imported_count} 条，更新 {updated_count} 条记录'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'导入失败: {str(e)}'})

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
