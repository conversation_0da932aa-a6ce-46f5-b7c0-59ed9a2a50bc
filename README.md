# 🚀 AI驱动的商机报价系统 - Gemini风格炫酷界面

这是一个基于Flask + Vue.js开发的智能商机报价系统，采用Gemini风格的炫酷界面设计，能够根据用户输入的商机信息、地区和岗位选择，自动生成详细的报价明细表，并具备完整的报价记录管理功能。

## ✨ 新特性

- 🎨 **Gemini风格界面**：采用现代化的玻璃态设计，炫酷的渐变效果和动画
- 🔄 **双计算模式**：支持按月和按天两种计算方式
- 📊 **智能考勤**：按天计算时支持考勤基数设置（精确到小数点后两位）
- 🎯 **商机选择**：商机项目支持下拉选择，便于快速选择
- 📋 **城市汇总**：报价明细表按城市分组显示，每个城市显示小计
- 🌟 **粒子背景**：动态粒子效果背景，提升视觉体验
- 📱 **响应式设计**：完美适配各种设备屏幕

## 功能特点

### 🎯 核心功能
- **智能报价生成**：根据商机、地区、岗位自动计算报价明细
- **多维度选择**：支持多地区、多岗位组合报价
- **成本计算**：自动计算工资、社保、公积金等各项成本
- **工期灵活**：支持自定义工期设置

### 📊 数据管理
- **商机管理**：商机信息的录入和维护
- **城市数据**：各城市社保公积金数据管理
- **岗位成本**：不同岗位工资成本维护
- **Excel导入**：支持批量导入城市数据

### 📈 报表功能
- **报价记录**：完整的历史报价记录查看
- **详细明细**：每个报价的详细明细展示
- **Excel导出**：支持单个和批量报价导出
- **数据统计**：报价数据的统计分析

## 技术架构

- **后端框架**：Flask + SQLAlchemy
- **前端框架**：Vue.js 3 + Vite
- **状态管理**：Pinia
- **样式框架**：Tailwind CSS
- **数据库**：SQLite
- **Excel处理**：pandas + openpyxl
- **特效库**：particles.js + GSAP
- **构建工具**：Vite

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd price

# 安装Python依赖
pip install -r requirements.txt

# 安装前端依赖
cd frontend
npm install
cd ..
```

### 2. 初始化数据库
```bash
# 初始化数据库和示例数据
python init_db.py
```

### 3. 启动服务

#### 启动后端服务（Flask）
```bash
python app.py
```
后端服务将在 http://localhost:5000 启动

#### 启动前端服务（Vue.js）
```bash
cd frontend
npm run dev
```
前端服务将在 http://localhost:3000 启动

### 4. 访问系统
打开浏览器访问 http://localhost:3000 即可使用炫酷的Gemini风格界面！

## 📁 项目结构

```
price/
├── app.py                    # Flask后端主应用
├── init_db.py               # 数据库初始化脚本
├── requirements.txt          # Python依赖包列表
├── README.md                 # 项目说明文档
├── frontend/                 # 前端项目目录
│   ├── package.json         # 前端依赖配置
│   ├── vite.config.js       # Vite构建配置
│   ├── tailwind.config.js   # Tailwind CSS配置
│   ├── index.html           # 入口HTML文件
│   └── src/                 # 前端源码
│       ├── main.js          # 前端入口文件
│       ├── App.vue          # 主应用组件
│       ├── styles/          # 样式文件
│       │   └── main.css     # 主样式文件（Gemini风格）
│       ├── pages/           # 页面组件
│       │   ├── Home.vue     # 炫酷首页
│       │   ├── DataManagement.vue    # 数据管理
│       │   ├── QuotationGenerator.vue # 智能报价生成
│       │   └── QuotationRecords.vue   # 报价记录管理
│       ├── components/      # 通用组件
│       │   ├── Icons.vue    # 图标组件库
│       │   └── *.vue        # 各种模态框组件
│       └── stores/          # Pinia状态管理
│           ├── notification.js # 通知系统
│           └── quotation.js    # 报价数据管理
├── templates/                # Flask模板（兼容性保留）
├── static/                   # 静态文件目录
└── 城市社保公积金导入模板.xlsx # Excel导入模板
```

## 使用指南

### 数据准备

#### 1. 商机管理
- 进入"数据管理" -> "商机管理"
- 点击"添加商机"录入商机信息
- 商机名称将在报价时使用

#### 2. 城市社保公积金数据
- 进入"数据管理" -> "城市社保公积金"
- 可以手动添加或通过Excel批量导入
- Excel格式要求：
  - 城市名称
  - 个人缴纳金额
  - 公司缴纳金额

#### 3. 岗位成本数据
- 进入"数据管理" -> "岗位成本"
- 录入不同岗位的月工资标准

### 生成报价

#### 1. 设置报价参数
- 进入"生成报价"页面
- 输入商机名称（支持自动补全）
- 设置工期（月）
- 选择目标城市（可多选）
- 选择需要的岗位（可多选）

#### 2. 生成和查看
- 点击"生成报价"按钮
- 系统自动计算各项成本
- 显示详细的报价明细表
- 支持导出为Excel文件

### 报价管理

#### 1. 查看记录
- 进入"报价记录"页面
- 查看所有历史报价记录
- 支持查看详细明细

#### 2. 导出功能
- 单个报价导出：点击对应记录的"导出"按钮
- 批量导出：点击"导出所有记录"按钮
- Excel文件包含汇总信息和详细明细

## 数据库结构

### 主要数据表

1. **opportunities**（商机表）
   - id：主键
   - name：商机名称
   - description：描述
   - created_at：创建时间

2. **city_social_insurance**（城市社保公积金表）
   - id：主键
   - city_name：城市名称
   - personal_amount：个人缴纳金额
   - company_amount：公司缴纳金额

3. **position_costs**（岗位成本表）
   - id：主键
   - position_name：岗位名称
   - monthly_salary：月工资

4. **quotation_records**（报价记录表）
   - id：主键
   - opportunity_name：商机名称
   - selected_cities：选择的城市（JSON）
   - selected_positions：选择的岗位（JSON）
   - work_duration_months：工期
   - total_amount：总金额

5. **quotation_details**（报价明细表）
   - id：主键
   - quotation_id：报价记录ID
   - city_name：城市名称
   - position_name：岗位名称
   - monthly_salary：月工资
   - personal_insurance：个人社保公积金
   - company_insurance：公司社保公积金
   - subtotal：小计金额

## 计算逻辑

### 报价计算公式

#### 按月计算
```
月度总成本 = 月工资 + 个人社保公积金 + 公司社保公积金
工期小计 = 月度总成本 × 工期（月）
```

#### 按天计算
```
月度总成本 = 日工资 × 考勤基数 + 公司社保公积金
工期小计 = 月度总成本 × 工期（天）
```

#### 总计算
```
总报价 = 所有组合的工期小计之和
```

### 组合计算
系统会为每个"城市-岗位"组合生成一条明细记录，最终汇总所有明细得出总报价。

## 扩展功能

系统设计具有良好的扩展性，可以轻松添加以下功能：

- 报价审批流程
- 更复杂的成本计算模型
- 报价模板管理
- 客户管理
- 合同管理
- 数据分析和图表展示

## 注意事项

1. **数据备份**：定期备份SQLite数据库文件
2. **Excel格式**：导入Excel时请确保列名完全匹配
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari等）
4. **数据精度**：金额计算保留两位小数

## 技术支持

如有问题或建议，请联系开发团队。
