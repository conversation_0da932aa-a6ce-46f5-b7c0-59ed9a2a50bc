# 岗位成本管理功能更新

## 更新内容

### 1. 岗位成本列表筛选功能

#### 新增功能
- **按岗位名称筛选**: 支持模糊搜索岗位名称
- **按岗位级别筛选**: 支持按初级、中级、高级、专家级别筛选
- **按城市筛选**: 支持按城市名称筛选
- **组合筛选**: 支持多个条件同时筛选
- **实时筛选**: 输入筛选条件后自动更新结果
- **清除筛选**: 一键清除所有筛选条件

#### 技术实现
- 前端：在 `frontend/src/pages/DataManagement.vue` 中添加筛选UI组件
- 后端：利用现有的 `/api/positions` API，支持 `position_name`、`position_level`、`city_name` 参数
- 筛选逻辑：后端数据库查询，支持分页和筛选组合

#### 界面特点
- 筛选区域采用玻璃卡片样式，与整体设计风格一致
- 筛选条件布局为响应式网格，适配不同屏幕尺寸
- 实时显示筛选结果数量
- 提供清除筛选按钮

### 2. 岗位成本添加编辑页面样式优化

#### 样式调整
- **高度优化**: 模态框最大高度调整为90vh，内容区域最大高度60vh
- **滚动优化**: 内容区域支持垂直滚动，避免内容溢出
- **按钮固定**: 操作按钮固定在底部，使用sticky定位
- **样式统一**: 参考城市数据模态框的样式设计

#### 具体改进
- **AddPositionModal**: 
  - 调整模态框结构，使用flex布局
  - 内容区域可滚动，按钮固定在底部
  - 优化加载状态显示
  
- **EditPositionModal**:
  - 与添加模态框保持一致的样式
  - 优化按钮布局和加载状态
  - 保持预览功能

#### 技术细节
- 使用 `max-height: 90vh` 限制模态框高度
- 内容区域使用 `overflow-y-auto` 实现滚动
- 按钮区域使用 `sticky bottom-0` 固定在底部
- 保持与城市模态框相同的设计语言

## 文件修改清单

### 前端文件
1. `frontend/src/pages/DataManagement.vue`
   - 添加筛选UI组件
   - 实现筛选逻辑和状态管理
   - 集成后端API调用

2. `frontend/src/stores/quotation.js`
   - 修改 `fetchPositions` 方法支持筛选参数
   - 优化API调用逻辑

3. `frontend/src/components/AddPositionModal.vue`
   - 调整模态框高度和布局
   - 优化按钮样式和定位

4. `frontend/src/components/EditPositionModal.vue`
   - 调整模态框高度和布局
   - 优化按钮样式和定位

### 后端文件
- 无需修改，现有API已支持筛选功能

## 使用说明

### 筛选功能使用
1. 在岗位成本管理页面，找到"筛选条件"区域
2. 在"岗位名称"输入框中输入关键词进行模糊搜索
3. 在"岗位级别"下拉框中选择特定级别
4. 在"城市"下拉框中选择特定城市
5. 筛选结果会实时显示在下方表格中
6. 点击"清除筛选"按钮可重置所有筛选条件

### 添加/编辑岗位
1. 点击"添加岗位"或编辑按钮打开模态框
2. 模态框现在具有更好的高度适配和滚动功能
3. 在内容较多时，可以滚动查看所有字段
4. 操作按钮始终固定在底部，方便操作

## 技术特点

- **响应式设计**: 筛选区域和模态框都支持响应式布局
- **性能优化**: 后端筛选减少前端数据处理负担
- **用户体验**: 实时筛选反馈，清晰的视觉层次
- **代码复用**: 复用现有的API和样式组件
- **一致性**: 与城市数据管理保持一致的交互模式 