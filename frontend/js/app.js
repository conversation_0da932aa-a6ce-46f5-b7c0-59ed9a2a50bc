// 全局变量
const API_BASE = '/api';
let currentPage = 'dashboard';
let jobs = [];
let resumes = [];
let projects = [];
let currentEditingJob = null;
let selectedResumeIds = [];
let allResumesForSelector = [];

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadDashboardData();
});

// 初始化应用
function initializeApp() {
    // 设置页面切换
    document.querySelectorAll('.menu-item').forEach(item => {
        item.addEventListener('click', function() {
            const page = this.dataset.page;
            switchPage(page);
        });
    });
    
    // 设置模态框关闭
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 岗位表单提交
    document.getElementById('job-form').addEventListener('submit', handleJobSubmit);
    
    // 文件上传
    const fileInput = document.getElementById('file-input');
    const uploadArea = document.getElementById('upload-area');
    
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleFileDrop);
    fileInput.addEventListener('change', handleFileSelect);
}

// 页面切换
function switchPage(page) {
    // 更新菜单状态
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-page="${page}"]`).classList.add('active');
    
    // 更新页面显示
    document.querySelectorAll('.page').forEach(p => {
        p.classList.remove('active');
    });
    document.getElementById(`${page}-page`).classList.add('active');
    
    currentPage = page;
    
    // 加载页面数据
    switch(page) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'jobs':
            loadJobs();
            break;
        case 'resumes':
            loadResumes();
            break;
        case 'analysis':
            loadAnalysisData();
            break;
    }
}

// 加载仪表板数据
async function loadDashboardData() {
    try {
        showLoading();

        const [jobsResponse, resumesResponse, modelsResponse] = await Promise.all([
            fetch(`${API_BASE}/jobs/`),
            fetch(`${API_BASE}/resumes/`),
            fetch(`${API_BASE}/models/status`)
        ]);

        const jobsData = await jobsResponse.json();
        const resumesData = await resumesResponse.json();
        const modelsData = await modelsResponse.json();

        // 更新统计数据
        document.getElementById('total-jobs').textContent = jobsData.length;
        document.getElementById('total-resumes').textContent = resumesData.length;

        // 获取匹配结果统计
        const matchResponse = await fetch(`${API_BASE}/matches/results`);
        const matchData = await matchResponse.json();
        document.getElementById('total-matches').textContent = matchData.length;

        // 计算平均匹配度
        const avgScore = matchData.length > 0
            ? matchData.reduce((sum, match) => sum + (match.match_score || 0), 0) / matchData.length
            : 0;
        document.getElementById('avg-score').textContent = `${avgScore.toFixed(1)}%`;

        // 显示模型状态
        displayModelStatus(modelsData);

        hideLoading();
    } catch (error) {
        console.error('加载仪表板数据失败:', error);
        showNotification('加载数据失败', 'error');
        hideLoading();
    }
}

// 显示模型状态
function displayModelStatus(modelsData) {
    const statusContainer = document.getElementById('model-status');
    if (!statusContainer) {
        // 如果容器不存在，创建一个
        const container = document.createElement('div');
        container.id = 'model-status';
        container.className = 'model-status';

        const quickActions = document.querySelector('.quick-actions');
        if (quickActions) {
            quickActions.appendChild(container);
        }
    }

    const models = modelsData.models || {};
    const currentProvider = modelsData.current_provider || 'unknown';

    let statusHtml = '<h3>AI模型状态</h3><div class="model-status-grid">';

    // DeepSeek状态
    const deepseekStatus = models.deepseek ? '🟢 在线' : '🔴 离线';
    const deepseekClass = models.deepseek ? 'status-online' : 'status-offline';
    statusHtml += `
        <div class="model-item ${deepseekClass}">
            <strong>DeepSeek Chat</strong>
            <span>${deepseekStatus}</span>
            ${currentProvider === 'deepseek' ? '<span class="current-model">当前使用</span>' : ''}
        </div>
    `;

    // Qwen状态
    const qwenStatus = models.qwen ? '🟢 在线' : '🔴 离线';
    const qwenClass = models.qwen ? 'status-online' : 'status-offline';
    statusHtml += `
        <div class="model-item ${qwenClass}">
            <strong>Qwen-VL-Max</strong>
            <span>${qwenStatus}</span>
            ${currentProvider === 'qwen' ? '<span class="current-model">当前使用</span>' : ''}
        </div>
    `;

    statusHtml += '</div>';

    if (!models.deepseek && !models.qwen) {
        statusHtml += '<p class="model-warning">⚠️ 所有AI模型离线，将使用模拟模式</p>';
    }

    document.getElementById('model-status').innerHTML = statusHtml;
}

// 加载岗位数据
async function loadJobs() {
    try {
        showLoading();
        const response = await fetch(`${API_BASE}/jobs/`);
        jobs = await response.json();
        renderJobs(jobs);
        hideLoading();
    } catch (error) {
        console.error('加载岗位失败:', error);
        showNotification('加载岗位失败', 'error');
        hideLoading();
    }
}

// 渲染岗位列表
function renderJobs(jobList) {
    const grid = document.getElementById('jobs-grid');
    grid.innerHTML = '';
    
    jobList.forEach(job => {
        const jobCard = createJobCard(job);
        grid.appendChild(jobCard);
    });
}

// 创建岗位卡片
function createJobCard(job) {
    const card = document.createElement('div');
    card.className = 'job-card';
    
    const createdDate = new Date(job.created_at).toLocaleDateString('zh-CN');
    
    card.innerHTML = `
        <div class="card-header">
            <div>
                <h3 class="card-title">${job.title}</h3>
                <p class="card-meta">创建时间: ${createdDate}</p>
            </div>
            <div class="card-actions">
                <button class="btn btn-secondary btn-small" onclick="editJob(${job.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-secondary btn-small" onclick="deleteJob(${job.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="card-content">
            <p>${job.description.substring(0, 150)}${job.description.length > 150 ? '...' : ''}</p>
        </div>
    `;
    
    return card;
}

// 显示岗位模态框
function showJobModal(job = null) {
    currentEditingJob = job;
    const modal = document.getElementById('job-modal');
    const title = document.getElementById('job-modal-title');
    const form = document.getElementById('job-form');
    
    if (job) {
        title.textContent = '编辑岗位';
        document.getElementById('job-title').value = job.title;
        document.getElementById('job-description').value = job.description;
        document.getElementById('job-requirements').value = job.requirements || '';
    } else {
        title.textContent = '创建岗位';
        form.reset();
    }
    
    modal.style.display = 'block';
}

// 处理岗位表单提交
async function handleJobSubmit(event) {
    event.preventDefault();
    
    const formData = {
        title: document.getElementById('job-title').value,
        description: document.getElementById('job-description').value,
        requirements: document.getElementById('job-requirements').value
    };
    
    try {
        showLoading();
        
        let response;
        if (currentEditingJob) {
            response = await fetch(`${API_BASE}/jobs/${currentEditingJob.id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });
        } else {
            response = await fetch(`${API_BASE}/jobs/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });
        }
        
        if (response.ok) {
            closeModal('job-modal');
            loadJobs();
            showNotification(currentEditingJob ? '岗位更新成功' : '岗位创建成功', 'success');
        } else {
            throw new Error('操作失败');
        }
        
        hideLoading();
    } catch (error) {
        console.error('岗位操作失败:', error);
        showNotification('操作失败', 'error');
        hideLoading();
    }
}

// 编辑岗位
function editJob(jobId) {
    const job = jobs.find(j => j.id === jobId);
    if (job) {
        showJobModal(job);
    }
}

// 删除岗位
async function deleteJob(jobId) {
    if (!confirm('确定要删除这个岗位吗？')) {
        return;
    }
    
    try {
        showLoading();
        const response = await fetch(`${API_BASE}/jobs/${jobId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            loadJobs();
            showNotification('岗位删除成功', 'success');
        } else {
            throw new Error('删除失败');
        }
        
        hideLoading();
    } catch (error) {
        console.error('删除岗位失败:', error);
        showNotification('删除失败', 'error');
        hideLoading();
    }
}

// 搜索岗位
function searchJobs() {
    const keyword = document.getElementById('job-search').value.toLowerCase();
    const filteredJobs = jobs.filter(job => 
        job.title.toLowerCase().includes(keyword) ||
        job.description.toLowerCase().includes(keyword)
    );
    renderJobs(filteredJobs);
}

// 加载简历数据
async function loadResumes() {
    try {
        showLoading();
        const response = await fetch(`${API_BASE}/resumes/`);
        resumes = await response.json();
        renderResumes(resumes);
        hideLoading();
    } catch (error) {
        console.error('加载简历失败:', error);
        showNotification('加载简历失败', 'error');
        hideLoading();
    }
}

// 渲染简历列表
function renderResumes(resumeList) {
    const grid = document.getElementById('resumes-grid');
    grid.innerHTML = '';
    
    resumeList.forEach(resume => {
        const resumeCard = createResumeCard(resume);
        grid.appendChild(resumeCard);
    });
}

// 创建简历卡片
function createResumeCard(resume) {
    const card = document.createElement('div');
    card.className = 'resume-card';
    
    const createdDate = new Date(resume.created_at).toLocaleDateString('zh-CN');
    const fileSize = resume.file_size ? `${(resume.file_size / 1024).toFixed(1)} KB` : '未知';
    
    card.innerHTML = `
        <div class="card-header">
            <div>
                <h3 class="card-title">${resume.original_filename}</h3>
                <p class="card-meta">上传时间: ${createdDate} | 大小: ${fileSize}</p>
            </div>
            <div class="card-actions">
                <button class="btn btn-secondary btn-small" onclick="viewResumeText(${resume.id})">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-secondary btn-small" onclick="deleteResume(${resume.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="card-content">
            <p>文件类型: ${resume.file_type.toUpperCase()}</p>
            <p>${resume.extracted_text ? resume.extracted_text.substring(0, 100) + '...' : '文本提取失败'}</p>
        </div>
    `;
    
    return card;
}

// 显示上传模态框
function showUploadModal() {
    document.getElementById('upload-modal').style.display = 'block';
    document.getElementById('upload-progress').style.display = 'none';
}

// 处理文件拖拽
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    uploadFiles(files);
}

function handleFileSelect(event) {
    const files = event.target.files;
    uploadFiles(files);
}

// 上传文件
async function uploadFiles(files) {
    const progressDiv = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const statusText = document.getElementById('upload-status');
    
    progressDiv.style.display = 'block';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);
        
        try {
            statusText.textContent = `上传中... (${i + 1}/${files.length})`;
            progressFill.style.width = `${((i + 1) / files.length) * 100}%`;
            
            const response = await fetch(`${API_BASE}/resumes/upload`, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`上传失败: ${file.name}`);
            }
            
        } catch (error) {
            console.error('文件上传失败:', error);
            showNotification(`上传失败: ${file.name}`, 'error');
        }
    }
    
    closeModal('upload-modal');
    loadResumes();
    showNotification('文件上传完成', 'success');
}

// 查看简历文本
async function viewResumeText(resumeId) {
    try {
        const response = await fetch(`${API_BASE}/resumes/${resumeId}/text`);
        const data = await response.json();
        
        alert(`简历文本内容:\n\n${data.extracted_text}`);
    } catch (error) {
        console.error('获取简历文本失败:', error);
        showNotification('获取简历文本失败', 'error');
    }
}

// 删除简历
async function deleteResume(resumeId) {
    if (!confirm('确定要删除这个简历吗？')) {
        return;
    }
    
    try {
        showLoading();
        const response = await fetch(`${API_BASE}/resumes/${resumeId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            loadResumes();
            showNotification('简历删除成功', 'success');
        } else {
            throw new Error('删除失败');
        }
        
        hideLoading();
    } catch (error) {
        console.error('删除简历失败:', error);
        showNotification('删除失败', 'error');
        hideLoading();
    }
}

// 搜索简历
function searchResumes() {
    const keyword = document.getElementById('resume-search').value.toLowerCase();
    const filteredResumes = resumes.filter(resume =>
        resume.original_filename.toLowerCase().includes(keyword) ||
        (resume.extracted_text && resume.extracted_text.toLowerCase().includes(keyword))
    );
    renderResumes(filteredResumes);
}

// 加载分析页面数据
async function loadAnalysisData() {
    try {
        showLoading();

        // 加载岗位选项
        const jobsResponse = await fetch(`${API_BASE}/jobs/`);
        const jobsData = await jobsResponse.json();

        const jobSelect = document.getElementById('analysis-job-select');
        jobSelect.innerHTML = '<option value="">请选择岗位...</option>';
        jobsData.forEach(job => {
            const option = document.createElement('option');
            option.value = job.id;
            option.textContent = job.title;
            jobSelect.appendChild(option);
        });

        // 加载简历选项
        const resumesResponse = await fetch(`${API_BASE}/resumes/`);
        const resumesData = await resumesResponse.json();

        const resumeList = document.getElementById('analysis-resume-list');
        resumeList.innerHTML = '';
        resumesData.forEach(resume => {
            const resumeItem = document.createElement('div');
            resumeItem.className = 'resume-item';
            resumeItem.innerHTML = `
                <input type="checkbox" id="resume-${resume.id}" value="${resume.id}">
                <label for="resume-${resume.id}">${resume.original_filename}</label>
            `;
            resumeList.appendChild(resumeItem);
        });

        hideLoading();
    } catch (error) {
        console.error('加载分析数据失败:', error);
        showNotification('加载分析数据失败', 'error');
        hideLoading();
    }
}

// 开始分析
async function startAnalysis() {
    const jobId = document.getElementById('analysis-job-select').value;
    const selectedResumes = Array.from(document.querySelectorAll('#analysis-resume-list input:checked'))
        .map(input => parseInt(input.value));

    if (!jobId) {
        showNotification('请选择岗位', 'warning');
        return;
    }

    if (selectedResumes.length === 0) {
        showNotification('请选择至少一个简历', 'warning');
        return;
    }

    const startBtn = document.getElementById('start-analysis-btn');
    const resultsDiv = document.getElementById('analysis-results');

    startBtn.disabled = true;
    startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';

    resultsDiv.innerHTML = '<div class="progress-indicator">正在初始化分析...</div>';

    try {
        // 使用SSE接收流式结果
        const eventSource = new EventSource(`${API_BASE}/matches/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                job_id: parseInt(jobId),
                resume_ids: selectedResumes
            })
        });

        // 由于EventSource不支持POST，我们使用fetch with stream
        const response = await fetch(`${API_BASE}/matches/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                job_id: parseInt(jobId),
                resume_ids: selectedResumes
            })
        });

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        handleAnalysisEvent(data, resultsDiv);
                    } catch (e) {
                        console.error('解析SSE数据失败:', e);
                    }
                }
            }
        }

    } catch (error) {
        console.error('分析失败:', error);
        showNotification('分析失败', 'error');
        resultsDiv.innerHTML = '<div class="progress-indicator">分析失败，请重试</div>';
    } finally {
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-magic"></i> 开始分析';
    }
}

// 处理分析事件
function handleAnalysisEvent(data, resultsDiv) {
    switch (data.type) {
        case 'progress':
            resultsDiv.innerHTML = `
                <div class="progress-indicator">
                    <p>正在分析: ${data.message}</p>
                    <p>进度: ${data.current}/${data.total}</p>
                </div>
            `;
            break;

        case 'result':
            if (data.status === 'success') {
                addAnalysisResult(data, resultsDiv);
            } else {
                console.error('分析结果错误:', data);
            }
            break;

        case 'complete':
            const completeDiv = document.createElement('div');
            completeDiv.className = 'progress-indicator';
            completeDiv.innerHTML = `
                <p style="color: #10b981; font-weight: 600;">✓ ${data.message}</p>
                <button class="btn btn-primary" onclick="exportResults()">
                    <i class="fas fa-download"></i> 导出Word报告
                </button>
            `;
            resultsDiv.appendChild(completeDiv);
            break;

        case 'error':
            resultsDiv.innerHTML = `<div class="progress-indicator" style="color: #ef4444;">错误: ${data.message}</div>`;
            break;

        case 'end':
            console.log('分析流结束');
            break;
    }
}

// 添加分析结果
function addAnalysisResult(data, resultsDiv) {
    // 如果是第一个结果，清空进度指示器
    if (resultsDiv.querySelector('.progress-indicator')) {
        resultsDiv.innerHTML = '';
    }

    const resultDiv = document.createElement('div');
    resultDiv.className = 'result-item';

    resultDiv.innerHTML = `
        <div class="result-header">
            <h3>${data.resume_filename}</h3>
            <div class="result-score">${data.match_score?.toFixed(1) || 0}/100</div>
        </div>
        <div class="result-content">
            <div class="markdown-content">${formatMarkdown(data.analysis_result)}</div>
        </div>
    `;

    resultsDiv.appendChild(resultDiv);
}

// 简单的Markdown格式化
function formatMarkdown(text) {
    if (!text) return '';

    return text
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^\* (.*$)/gim, '<li>$1</li>')
        .replace(/^- (.*$)/gim, '<li>$1</li>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
}

// 导出结果
async function exportResults() {
    const jobId = document.getElementById('analysis-job-select').value;
    const selectedResumes = Array.from(document.querySelectorAll('#analysis-resume-list input:checked'))
        .map(input => parseInt(input.value));

    try {
        showLoading();

        const response = await fetch(`${API_BASE}/matches/export/word`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                job_id: parseInt(jobId),
                resume_ids: selectedResumes
            })
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'match_results.docx';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showNotification('报告导出成功', 'success');
        } else {
            throw new Error('导出失败');
        }

        hideLoading();
    } catch (error) {
        console.error('导出失败:', error);
        showNotification('导出失败', 'error');
        hideLoading();
    }
}

// 工具函数
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function showLoading() {
    document.getElementById('loading').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

function showNotification(message, type = 'info') {
    const container = document.getElementById('notifications');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'exclamation-circle' :
                 type === 'warning' ? 'exclamation-triangle' : 'info-circle';

    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
    `;

    container.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);

    // 点击移除
    notification.addEventListener('click', () => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    });
}
