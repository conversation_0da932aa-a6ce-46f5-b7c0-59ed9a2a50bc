<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>前后端连接测试</h1>
    
    <div class="test-section">
        <h2>API连接测试</h2>
        <button onclick="testPositionsAPI()">测试岗位API</button>
        <button onclick="testCitiesAPI()">测试城市API</button>
        <button onclick="testOpportunitiesAPI()">测试商机API</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>岗位数据详情</h2>
        <div id="positions-details"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001';

        async function testAPI(endpoint, name) {
            try {
                console.log(`测试 ${name} API: ${API_BASE}${endpoint}`);
                const response = await fetch(`${API_BASE}${endpoint}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                return { success: true, data, status: response.status };
            } catch (error) {
                console.error(`${name} API 错误:`, error);
                return { success: false, error: error.message };
            }
        }

        async function testPositionsAPI() {
            const result = await testAPI('/api/positions', '岗位');
            const resultsDiv = document.getElementById('api-results');
            const detailsDiv = document.getElementById('positions-details');
            
            if (result.success) {
                const data = result.data;
                let positionsArray;
                
                if (data.positions) {
                    // 分页格式
                    positionsArray = data.positions;
                    resultsDiv.innerHTML += `<div class="success">✅ 岗位API成功 (分页格式): ${positionsArray.length} 个岗位，总数: ${data.total}</div>`;
                } else if (Array.isArray(data)) {
                    // 直接数组格式
                    positionsArray = data;
                    resultsDiv.innerHTML += `<div class="success">✅ 岗位API成功 (数组格式): ${positionsArray.length} 个岗位</div>`;
                } else {
                    resultsDiv.innerHTML += `<div class="error">❌ 岗位API返回格式异常</div>`;
                    return;
                }
                
                // 显示详细信息
                if (positionsArray.length > 0) {
                    let detailsHTML = '<h3>岗位数据样例:</h3>';
                    positionsArray.slice(0, 5).forEach((pos, index) => {
                        detailsHTML += `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                                <strong>${index + 1}. ${pos.position_name}</strong><br>
                                城市: ${pos.city_name}<br>
                                月薪: ¥${pos.monthly_salary}<br>
                                日薪: ¥${pos.daily_salary}<br>
                                ID: ${pos.id}
                            </div>
                        `;
                    });
                    detailsDiv.innerHTML = detailsHTML;
                } else {
                    detailsDiv.innerHTML = '<div class="error">❌ 没有岗位数据</div>';
                }
                
                console.log('岗位数据:', positionsArray);
            } else {
                resultsDiv.innerHTML += `<div class="error">❌ 岗位API失败: ${result.error}</div>`;
                detailsDiv.innerHTML = '<div class="error">无法获取岗位详情</div>';
            }
        }

        async function testCitiesAPI() {
            const result = await testAPI('/api/cities', '城市');
            const resultsDiv = document.getElementById('api-results');
            
            if (result.success) {
                const cities = result.data;
                resultsDiv.innerHTML += `<div class="success">✅ 城市API成功: ${cities.length} 个城市</div>`;
                console.log('城市数据:', cities);
            } else {
                resultsDiv.innerHTML += `<div class="error">❌ 城市API失败: ${result.error}</div>`;
            }
        }

        async function testOpportunitiesAPI() {
            const result = await testAPI('/api/opportunities', '商机');
            const resultsDiv = document.getElementById('api-results');
            
            if (result.success) {
                const opportunities = result.data;
                resultsDiv.innerHTML += `<div class="success">✅ 商机API成功: ${opportunities.length} 个商机</div>`;
                console.log('商机数据:', opportunities);
            } else {
                resultsDiv.innerHTML += `<div class="error">❌ 商机API失败: ${result.error}</div>`;
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            document.getElementById('api-results').innerHTML = '<div class="info">🔄 开始自动测试...</div>';
            setTimeout(() => {
                testPositionsAPI();
                setTimeout(() => testCitiesAPI(), 500);
                setTimeout(() => testOpportunitiesAPI(), 1000);
            }, 500);
        };
    </script>
</body>
</html>
