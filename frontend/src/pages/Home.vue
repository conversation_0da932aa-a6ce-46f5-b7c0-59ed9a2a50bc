<template>
  <div class="min-h-screen">
    <!-- 英雄区域 -->
    <section class="relative py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center">
          <!-- 主标题 -->
          <h1 class="text-5xl md:text-7xl font-bold mb-6 fade-in">
            <span class="gradient-text">天宇正清</span>
            <br>
            <span class="text-white">商机报价系统</span>
          </h1>
          
          <!-- 副标题 -->
          <p class="text-xl md:text-2xl text-white/70 mb-8 max-w-3xl mx-auto slide-up">
            智能的商机报价系统，支持多地区、多岗位的智能成本计算，
            让报价更精准、更高效、更专业
          </p>
          
          <!-- 操作按钮 -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 slide-up">
            <router-link to="/quotation" class="btn-gemini text-lg px-8 py-4 glow-hover">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
              </svg>
              开始报价
            </router-link>
            <router-link to="/data" class="glass-card px-8 py-4 text-white hover:bg-white/10 transition-all duration-300">
              <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7M4 7c0-2.21 1.79-4 4-4h8c2.21 0 4-1.79 4-4M4 7h16m-1 4l-3 3h4l-3-3"/>
              </svg>
              数据管理
            </router-link>
          </div>
        </div>
      </div>
      
      <!-- 浮动元素 -->
      <div class="absolute top-20 left-10 w-20 h-20 bg-blue-500/20 rounded-full blur-xl float"></div>
      <div class="absolute top-40 right-20 w-32 h-32 bg-purple-500/20 rounded-full blur-xl float" style="animation-delay: -2s;"></div>
      <div class="absolute bottom-20 left-1/4 w-24 h-24 bg-pink-500/20 rounded-full blur-xl float" style="animation-delay: -4s;"></div>
    </section>

    <!-- 功能特点 -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold gradient-text mb-4">核心功能</h2>
          <p class="text-xl text-white/70">强大的功能，简单的操作</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="(feature, index) in features" :key="index" 
               class="glass-card p-6 text-center hover:scale-105 transition-all duration-300"
               :style="{ animationDelay: `${index * 0.1}s` }">
            <div class="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
              <component :is="feature.icon" class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-xl font-semibold text-white mb-2">{{ feature.title }}</h3>
            <p class="text-white/70">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="py-20 px-4 sm:px-6 lg:px-8" v-if="stats">
      <div class="max-w-7xl mx-auto">
        <div class="glass-card p-8 rounded-3xl">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div v-for="(stat, index) in stats" :key="index" class="fade-in">
              <div class="text-4xl font-bold gradient-text mb-2">{{ stat.value }}</div>
              <div class="text-white/70">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 工作流程 -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold gradient-text mb-4">工作流程</h2>
          <p class="text-xl text-white/70">简单四步，完成专业报价</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div v-for="(step, index) in steps" :key="index" 
               class="relative text-center slide-up"
               :style="{ animationDelay: `${index * 0.2}s` }">
            <!-- 连接线 -->
            <div v-if="index < steps.length - 1" 
                 class="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 transform -translate-x-1/2 z-0"></div>
            
            <!-- 步骤内容 -->
            <div class="relative z-10">
              <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-xl">
                {{ index + 1 }}
              </div>
              <h3 class="text-xl font-semibold text-white mb-2">{{ step.title }}</h3>
              <p class="text-white/70">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto text-center">
        <div class="glass-card p-12 rounded-3xl">
          <h2 class="text-4xl font-bold text-white mb-4">准备开始了吗？</h2>
          <p class="text-xl text-white/70 mb-8">立即体验AI驱动的智能报价系统</p>
          <router-link to="/quotation" class="btn-gemini text-lg px-8 py-4 glow-hover">
            立即开始报价
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import Icons from '../components/Icons.vue'

export default {
  name: 'Home',
  components: {
    ...Icons
  },
  setup() {
    const quotationStore = useQuotationStore()
    
    const features = [
      {
        icon: 'CalculatorIcon',
        title: '智能计算',
        description: '自动计算工资、社保、公积金等各项成本'
      },
      {
        icon: 'GlobeIcon',
        title: '多地区支持',
        description: '支持全国各城市的社保公积金标准'
      },
      {
        icon: 'UsersIcon',
        title: '多岗位选择',
        description: '灵活选择不同岗位组合报价'
      },
      {
        icon: 'DocumentTextIcon',
        title: '报表导出',
        description: '一键导出专业的Excel报价表'
      }
    ]
    
    const steps = [
      {
        title: '数据准备',
        description: '录入商机、城市、岗位等基础数据'
      },
      {
        title: '参数设置',
        description: '选择地区、岗位、工期等报价参数'
      },
      {
        title: '智能计算',
        description: 'AI自动计算各项成本和总价'
      },
      {
        title: '导出报表',
        description: '生成专业的报价明细表'
      }
    ]
    
    const stats = computed(() => {
      if (!quotationStore.opportunities.length) return null
      
      return [
        {
          value: quotationStore.opportunities.length,
          label: '商机项目'
        },
        {
          value: quotationStore.cities.length,
          label: '支持城市'
        },
        {
          value: quotationStore.positions.length,
          label: '岗位类型'
        },
        {
          value: quotationStore.totalRecords,
          label: '报价记录'
        }
      ]
    })
    
    onMounted(async () => {
      try {
        await quotationStore.initializeData()
      } catch (error) {
        console.error('初始化数据失败:', error)
      }
    })
    
    return {
      features,
      steps,
      stats
    }
  }
}
</script>

<style scoped>
.fade-in {
  animation: fadeIn 1s ease-out;
}

.slide-up {
  animation: slideUp 1s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
