@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --gemini-blue: #4285f4;
  --gemini-purple: #9c27b0;
  --gemini-pink: #e91e63;
  --gemini-orange: #ff9800;
  --gemini-green: #4caf50;
  --dark-bg: #0a0a0a;
  --card-bg: rgba(255, 255, 255, 0.05);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Google Sans', sans-serif;
  background: var(--dark-bg);
  color: var(--text-primary);
  overflow-x: hidden;
  line-height: 1.6;
}

/* 玻璃态效果 */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

.glass-card {
  background: var(--card-bg);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Gemini渐变按钮 */
.btn-gemini {
  background: linear-gradient(135deg, var(--gemini-blue), var(--gemini-purple));
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-gemini::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-gemini:hover::before {
  left: 100%;
}

.btn-gemini:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(66, 133, 244, 0.4);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

.float {
  animation: float 6s ease-in-out infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, var(--gemini-blue), var(--gemini-purple), var(--gemini-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* 发光效果 */
.glow {
  box-shadow: 0 0 20px rgba(66, 133, 244, 0.3);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(66, 133, 244, 0.5);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--gemini-blue), var(--gemini-purple));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--gemini-purple), var(--gemini-pink));
}

/* 输入框样式 */
.input-gemini {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.input-gemini:focus {
  outline: none;
  border-color: var(--gemini-blue);
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.input-gemini::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 选择框样式 */
.select-gemini {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.select-gemini:focus {
  outline: none;
  border-color: var(--gemini-blue);
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
}

/* 复选框样式 */
.checkbox-gemini {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-gemini:checked {
  background: linear-gradient(135deg, var(--gemini-blue), var(--gemini-purple));
  border-color: var(--gemini-blue);
}

.checkbox-gemini:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 加载动画 */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .glass-card {
    margin: 10px;
    border-radius: 12px;
  }
  
  .btn-gemini {
    padding: 10px 20px;
    font-size: 14px;
  }
}
