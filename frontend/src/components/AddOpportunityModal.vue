<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative glass-card p-6 w-full max-w-md">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-white">添加商机</h3>
        <button @click="$emit('close')" class="text-white/70 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            商机名称 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.name"
            type="text"
            class="input-gemini w-full"
            placeholder="请输入商机名称"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-white mb-2">描述</label>
          <textarea
            v-model="form.description"
            class="input-gemini w-full h-24 resize-none"
            placeholder="请输入商机描述（可选）"
          ></textarea>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="glass-card px-4 py-2 text-white hover:bg-white/10 transition-all"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="loading || !form.name.trim()"
            class="btn-gemini px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="loading" class="animate-spin w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'

export default {
  name: 'AddOpportunityModal',
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()
    
    const form = ref({
      name: '',
      description: ''
    })
    
    const loading = ref(false)
    
    const handleSubmit = async () => {
      if (!form.value.name.trim()) {
        notificationStore.warning('请输入商机名称')
        return
      }
      
      try {
        loading.value = true
        
        const result = await quotationStore.addOpportunity({
          name: form.value.name.trim(),
          description: form.value.description.trim()
        })
        
        if (result.success) {
          emit('success')
          emit('close')
        } else {
          notificationStore.error(result.message || '添加失败')
        }
      } catch (error) {
        console.error('添加商机失败:', error)
        notificationStore.error('添加失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      loading,
      handleSubmit
    }
  }
}
</script>
