{% extends "base.html" %}

{% block title %}首页 - 商机报价系统{% endblock %}

{% block content %}
<div id="alerts"></div>

<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-light p-5 rounded">
            <h1 class="display-4">欢迎使用商机报价系统</h1>
            <p class="lead">AI驱动的智能报价系统，帮助您快速生成准确的商机报价明细表。</p>
            <hr class="my-4">
            <p>系统支持多地区、多岗位的报价计算，包含社保公积金成本，并提供完整的报价记录管理功能。</p>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-file-earmark-text display-4 text-primary"></i>
                <h5 class="card-title mt-3">生成报价</h5>
                <p class="card-text">选择商机、地区和岗位，快速生成详细报价明细表。</p>
                <a href="{{ url_for('quotation') }}" class="btn btn-primary">开始报价</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-list-ul display-4 text-success"></i>
                <h5 class="card-title mt-3">报价记录</h5>
                <p class="card-text">查看和管理所有历史报价记录，支持导出Excel。</p>
                <a href="{{ url_for('quotation_records') }}" class="btn btn-success">查看记录</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-building display-4 text-info"></i>
                <h5 class="card-title mt-3">城市数据</h5>
                <p class="card-text">管理各城市的社保公积金数据，支持Excel导入。</p>
                <a href="{{ url_for('cities') }}" class="btn btn-info">管理数据</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-person-badge display-4 text-warning"></i>
                <h5 class="card-title mt-3">岗位成本</h5>
                <p class="card-text">维护不同岗位的工资成本数据。</p>
                <a href="{{ url_for('positions') }}" class="btn btn-warning">管理岗位</a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">系统功能特点</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-check-circle text-success"></i> 数据管理</h6>
                        <ul>
                            <li>商机信息录入和管理</li>
                            <li>城市社保公积金数据维护</li>
                            <li>岗位工资成本管理</li>
                            <li>支持Excel批量导入</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-check-circle text-success"></i> 报价功能</h6>
                        <ul>
                            <li>多地区多岗位选择</li>
                            <li>自动计算社保公积金成本</li>
                            <li>工期灵活设置</li>
                            <li>报价明细自动生成</li>
                        </ul>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6><i class="bi bi-check-circle text-success"></i> 记录管理</h6>
                        <ul>
                            <li>完整的报价历史记录</li>
                            <li>详细的报价明细查看</li>
                            <li>支持单个报价导出</li>
                            <li>批量报价记录导出</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-check-circle text-success"></i> 导出功能</h6>
                        <ul>
                            <li>Excel格式报价明细表</li>
                            <li>包含汇总信息</li>
                            <li>支持批量导出</li>
                            <li>文件命名规范化</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
