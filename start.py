#!/usr/bin/env python3
"""
音视频转文字工具启动脚本
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖安装完成!")
    except subprocess.CalledProcessError:
        print("错误: 依赖安装失败")
        sys.exit(1)

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import fastapi
        import whisperx
        import torch
        import sqlalchemy
        return True
    except ImportError:
        return False

def create_directories():
    """创建必要的目录"""
    directories = ["uploads", "frontend"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("目录创建完成!")

def start_server(host="0.0.0.0", port=8000, reload=True):
    """启动服务器"""
    print("正在启动服务器...")
    print(f"服务器将在 http://localhost:{port} 启动")
    print("按 Ctrl+C 停止服务器")
    print("\n首次启动可能需要下载WhisperX模型，请耐心等待...")

    try:
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        cmd = [
            sys.executable, "-m", "uvicorn", "backend.main:app",
            "--host", host, "--port", str(port)
        ]
        if reload:
            cmd.append("--reload")

        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器时出错: {e}")

def show_help():
    """显示帮助信息"""
    print("""
音视频转文字工具 - 使用说明

用法:
    python start.py [选项]

选项:
    --help, -h          显示此帮助信息
    --install-deps      仅安装依赖，不启动服务器
    --host HOST         指定服务器主机地址 (默认: 0.0.0.0)
    --port PORT         指定服务器端口 (默认: 8000)
    --no-reload         禁用自动重载

示例:
    python start.py                    # 使用默认设置启动
    python start.py --port 9000       # 在端口9000启动
    python start.py --install-deps    # 仅安装依赖
    """)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="音视频转文字工具")
    parser.add_argument("--install-deps", action="store_true", help="仅安装依赖")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--no-reload", action="store_true", help="禁用自动重载")

    args = parser.parse_args()

    print("=" * 50)
    print("音视频转文字工具")
    print("=" * 50)

    # 检查Python版本
    check_python_version()

    # 检查依赖
    if not check_dependencies() or args.install_deps:
        print("正在安装依赖...")
        install_dependencies()
        if args.install_deps:
            print("依赖安装完成!")
            return

    # 创建目录
    create_directories()

    # 启动服务器
    start_server(args.host, args.port, not args.no_reload)

if __name__ == "__main__":
    main()
