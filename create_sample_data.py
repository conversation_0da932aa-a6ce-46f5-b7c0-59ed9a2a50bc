#!/usr/bin/env python3
"""
创建示例数据的脚本
"""
import pandas as pd
from app import db, PositionCost
from datetime import datetime

def create_sample_excel():
    """创建示例Excel文件"""
    # 城市社保公积金示例数据
    city_data = [
        {'城市名称': '北京', '社保基数': 5000.00, '社保缴纳比例': 16.0, '公积金基数': 5000.00, '公积金缴纳比例': 12.0},
        {'城市名称': '上海', '社保基数': 4800.00, '社保缴纳比例': 16.0, '公积金基数': 4800.00, '公积金缴纳比例': 12.0},
        {'城市名称': '广州', '社保基数': 4200.00, '社保缴纳比例': 16.0, '公积金基数': 4200.00, '公积金缴纳比例': 12.0},
        {'城市名称': '深圳', '社保基数': 4600.00, '社保缴纳比例': 16.0, '公积金基数': 4600.00, '公积金缴纳比例': 12.0},
        {'城市名称': '杭州', '社保基数': 4000.00, '社保缴纳比例': 16.0, '公积金基数': 4000.00, '公积金缴纳比例': 12.0},
        {'城市名称': '南京', '社保基数': 3800.00, '社保缴纳比例': 16.0, '公积金基数': 3800.00, '公积金缴纳比例': 12.0},
        {'城市名称': '武汉', '社保基数': 3500.00, '社保缴纳比例': 16.0, '公积金基数': 3500.00, '公积金缴纳比例': 12.0},
        {'城市名称': '成都', '社保基数': 3200.00, '社保缴纳比例': 16.0, '公积金基数': 3200.00, '公积金缴纳比例': 12.0},
    ]
    # 保证所有字段都不为None
    for city in city_data:
        if '公积金基数' not in city or city['公积金基数'] is None:
            city['公积金基数'] = city['社保基数']
        if '公积金缴纳比例' not in city or city['公积金缴纳比例'] is None:
            city['公积金缴纳比例'] = 12.0
    
    df = pd.DataFrame(city_data)
    df.to_excel('城市社保公积金导入模板.xlsx', index=False, sheet_name='城市数据')
    print("已创建城市社保公积金导入模板.xlsx")

def create_sample_database():
    """创建示例数据库数据"""
    from app import app, db, Opportunity, CitySocialInsurance
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 添加示例商机
        opportunities = [
            {'name': '某大型企业人力资源外包项目', 'description': '为某大型制造企业提供全国多地人力资源外包服务'},
            {'name': '互联网公司技术团队外包', 'description': '为互联网公司提供技术开发团队外包服务'},
            {'name': '金融机构客服中心项目', 'description': '为银行提供客服中心人员外包服务'},
        ]
        
        for opp_data in opportunities:
            existing = Opportunity.query.filter_by(name=opp_data['name']).first()
            if not existing:
                opportunity = Opportunity(**opp_data)
                db.session.add(opportunity)
        
        # 添加示例城市数据
        cities = [
            {'city_name': '北京', 'social_base': 5000.00, 'social_rate': 0.16, 'housing_base': 5000.00, 'housing_rate': 0.12, 'company_amount': 1400.00},
            {'city_name': '上海', 'social_base': 4800.00, 'social_rate': 0.16, 'housing_base': 4800.00, 'housing_rate': 0.12, 'company_amount': 1344.00},
            {'city_name': '广州', 'social_base': 4200.00, 'social_rate': 0.16, 'housing_base': 4200.00, 'housing_rate': 0.12, 'company_amount': 1176.00},
            {'city_name': '深圳', 'social_base': 4600.00, 'social_rate': 0.16, 'housing_base': 4600.00, 'housing_rate': 0.12, 'company_amount': 1288.00},
            {'city_name': '杭州', 'social_base': 4000.00, 'social_rate': 0.16, 'housing_base': 4000.00, 'housing_rate': 0.12, 'company_amount': 1120.00},
        ]
        
        for city_data in cities:
            existing = CitySocialInsurance.query.filter_by(city_name=city_data['city_name']).first()
            if not existing:
                city = CitySocialInsurance(**city_data)
                db.session.add(city)
        
        db.session.commit()
        print("示例数据已添加到数据库")

def create_position_cost_data():
    positions = [
        # 北京
        {'position_name': '软件开发工程师', 'position_level': '初级', 'city_name': '北京', 'monthly_salary': 12000, 'daily_salary': 545},
        {'position_name': '软件开发工程师', 'position_level': '中级', 'city_name': '北京', 'monthly_salary': 18000, 'daily_salary': 818},
        {'position_name': '软件开发工程师', 'position_level': '高级', 'city_name': '北京', 'monthly_salary': 25000, 'daily_salary': 1136},
        {'position_name': '测试工程师', 'position_level': '初级', 'city_name': '北京', 'monthly_salary': 10000, 'daily_salary': 455},
        {'position_name': '测试工程师', 'position_level': '高级', 'city_name': '北京', 'monthly_salary': 16000, 'daily_salary': 727},
        {'position_name': '产品经理', 'position_level': '', 'city_name': '北京', 'monthly_salary': 20000, 'daily_salary': 909},
        # 上海
        {'position_name': '软件开发工程师', 'position_level': '初级', 'city_name': '上海', 'monthly_salary': 13000, 'daily_salary': 591},
        {'position_name': '软件开发工程师', 'position_level': '中级', 'city_name': '上海', 'monthly_salary': 19000, 'daily_salary': 864},
        {'position_name': '软件开发工程师', 'position_level': '高级', 'city_name': '上海', 'monthly_salary': 26000, 'daily_salary': 1182},
        {'position_name': '测试工程师', 'position_level': '初级', 'city_name': '上海', 'monthly_salary': 11000, 'daily_salary': 500},
        {'position_name': '测试工程师', 'position_level': '高级', 'city_name': '上海', 'monthly_salary': 17000, 'daily_salary': 773},
        {'position_name': '产品经理', 'position_level': '', 'city_name': '上海', 'monthly_salary': 21000, 'daily_salary': 955},
        # 广州
        {'position_name': '软件开发工程师', 'position_level': '初级', 'city_name': '广州', 'monthly_salary': 11000, 'daily_salary': 500},
        {'position_name': '软件开发工程师', 'position_level': '中级', 'city_name': '广州', 'monthly_salary': 17000, 'daily_salary': 773},
        {'position_name': 'UI设计师', 'position_level': '', 'city_name': '广州', 'monthly_salary': 15000, 'daily_salary': 682},
        {'position_name': '运维工程师', 'position_level': '', 'city_name': '广州', 'monthly_salary': 14000, 'daily_salary': 636},
        {'position_name': '销售代表', 'position_level': '', 'city_name': '广州', 'monthly_salary': 9000, 'daily_salary': 409},
        # 深圳
        {'position_name': '软件开发工程师', 'position_level': '初级', 'city_name': '深圳', 'monthly_salary': 14000, 'daily_salary': 636},
        {'position_name': '软件开发工程师', 'position_level': '中级', 'city_name': '深圳', 'monthly_salary': 20000, 'daily_salary': 909},
        {'position_name': 'UI设计师', 'position_level': '', 'city_name': '深圳', 'monthly_salary': 16000, 'daily_salary': 727},
        {'position_name': '运维工程师', 'position_level': '', 'city_name': '深圳', 'monthly_salary': 15000, 'daily_salary': 682},
        {'position_name': '销售代表', 'position_level': '', 'city_name': '深圳', 'monthly_salary': 9500, 'daily_salary': 432},
        # 更多数据，便于分页
        {'position_name': '项目经理', 'position_level': '初级', 'city_name': '北京', 'monthly_salary': 18000, 'daily_salary': 818},
        {'position_name': '项目经理', 'position_level': '高级', 'city_name': '上海', 'monthly_salary': 25000, 'daily_salary': 1136},
        {'position_name': '客服专员', 'position_level': '', 'city_name': '广州', 'monthly_salary': 8000, 'daily_salary': 364},
        {'position_name': '客服专员', 'position_level': '', 'city_name': '深圳', 'monthly_salary': 8500, 'daily_salary': 386},
    ]
    for pos in positions:
        exists = PositionCost.query.filter_by(
            position_name=pos['position_name'],
            position_level=pos['position_level'],
            city_name=pos['city_name']
        ).first()
        if not exists:
            position = PositionCost(
                position_name=pos['position_name'],
                position_level=pos['position_level'],
                city_name=pos['city_name'],
                monthly_salary=pos['monthly_salary'],
                daily_salary=pos['daily_salary'],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.session.add(position)
    db.session.commit()
    print('岗位成本测试数据已添加')

if __name__ == '__main__':
    create_sample_excel()
    create_sample_database()
    from app import app
    with app.app_context():
        create_position_cost_data()
