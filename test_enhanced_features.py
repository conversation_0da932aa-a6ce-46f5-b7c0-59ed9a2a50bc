#!/usr/bin/env python3
"""
测试增强功能的脚本
"""
import requests
import json

def test_enhanced_features():
    """测试增强功能"""
    base_url = "http://localhost:5001"
    
    print("🧪 测试增强功能...")
    print("=" * 50)
    
    # 1. 测试岗位数据包含城市字段
    print("\n1. 测试岗位数据结构")
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            positions = response.json()
            print(f"✅ 岗位数据获取成功，共 {len(positions)} 个岗位")
            
            # 检查前几个岗位的数据结构
            for i, pos in enumerate(positions[:3]):
                print(f"   岗位 {i+1}: {pos['position_name']} - {pos['city_name']} - 月薪¥{pos['monthly_salary']} - 日薪¥{pos['daily_salary']}")
        else:
            print(f"❌ 岗位数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 岗位数据获取异常: {e}")
    
    # 2. 测试按城市获取岗位
    print("\n2. 测试按城市获取岗位")
    try:
        city_name = "北京"
        response = requests.get(f"{base_url}/api/positions/by_city/{city_name}")
        if response.status_code == 200:
            positions = response.json()
            print(f"✅ {city_name}的岗位获取成功，共 {len(positions)} 个岗位")
            for pos in positions[:3]:
                print(f"   - {pos['position_name']}: 月薪¥{pos['monthly_salary']}")
        else:
            print(f"❌ {city_name}的岗位获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ {city_name}的岗位获取异常: {e}")
    
    # 3. 测试新的报价生成（使用岗位-城市组合）
    print("\n3. 测试新的报价生成")
    try:
        quotation_data = {
            "opportunity_name": "测试增强功能项目",
            "calculation_type": "daily",
            "work_duration_days": 60,
            "quotation_coefficient": 1.15,
            "selected_cities": ["北京", "上海"],
            "selected_positions": [
                "软件开发工程师@北京",
                "项目经理@北京", 
                "软件开发工程师@上海",
                "UI设计师@上海"
            ]
        }
        
        response = requests.post(
            f"{base_url}/api/generate_quotation",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(quotation_data)
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 新报价生成成功")
                print(f"   - 报价ID: {result['quotation_id']}")
                print(f"   - 总金额: ¥{result['total_amount']:.2f}")
                print(f"   - 报价系数: {quotation_data['quotation_coefficient']}")
                print(f"   - 明细数量: {len(result['details'])}")
                
                # 显示明细
                print("   明细:")
                for detail in result['details']:
                    print(f"     {detail['city_name']} - {detail['position_name']}: ¥{detail['subtotal']:.2f}")
            else:
                print(f"❌ 新报价生成失败: {result['message']}")
        else:
            print(f"❌ 新报价生成请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 新报价生成异常: {e}")
    
    # 4. 测试按天计算的社保公积金折算
    print("\n4. 测试按天计算的社保公积金折算")
    try:
        # 获取一个城市的社保数据
        response = requests.get(f"{base_url}/api/cities")
        if response.status_code == 200:
            cities = response.json()
            if cities:
                city = cities[0]
                company_amount = city['company_amount']
                daily_company_amount = -(-company_amount // 30)  # 向上取整
                print(f"✅ 社保公积金折算测试")
                print(f"   城市: {city['city_name']}")
                print(f"   月度公司缴纳: ¥{company_amount}")
                print(f"   折算到日(向上取整): ¥{daily_company_amount}")
                print(f"   折算公式: math.ceil({company_amount} / 30) = {daily_company_amount}")
    except Exception as e:
        print(f"❌ 社保公积金折算测试异常: {e}")
    
    # 5. 测试删除功能
    print("\n5. 测试删除功能")
    try:
        # 先创建一个测试商机
        test_opportunity = {
            "name": "测试删除商机",
            "description": "这是一个用于测试删除功能的商机"
        }
        
        response = requests.post(
            f"{base_url}/api/opportunities",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_opportunity)
        )
        
        if response.status_code == 200:
            # 获取所有商机，找到刚创建的
            response = requests.get(f"{base_url}/api/opportunities")
            if response.status_code == 200:
                opportunities = response.json()
                test_opp = None
                for opp in opportunities:
                    if opp['name'] == "测试删除商机":
                        test_opp = opp
                        break
                
                if test_opp:
                    # 测试删除
                    response = requests.delete(f"{base_url}/api/opportunities/{test_opp['id']}")
                    if response.status_code == 200:
                        result = response.json()
                        if result['success']:
                            print(f"✅ 删除功能测试成功")
                            print(f"   - 成功删除商机: {test_opp['name']}")
                        else:
                            print(f"❌ 删除失败: {result['message']}")
                    else:
                        print(f"❌ 删除请求失败: {response.status_code}")
                else:
                    print("❌ 未找到测试商机")
    except Exception as e:
        print(f"❌ 删除功能测试异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试增强功能...")
    test_enhanced_features()
    
    print("\n" + "=" * 50)
    print("✨ 增强功能测试完成！")
    print("\n📋 新功能说明:")
    print("1. ✅ 岗位成本增加了城市字段")
    print("2. ✅ 支持按城市获取岗位数据")
    print("3. ✅ 报价生成支持岗位-城市组合")
    print("4. ✅ 按天计算时社保公积金向上取整折算")
    print("5. ✅ 数据管理支持编辑和删除功能")
    print("6. ✅ 生成报价页面使用弹框选择")
    print("7. ✅ 岗位选择与城市联动")
    
    print("\n🌐 访问地址:")
    print("- 前端界面: http://localhost:3000")
    print("- 后端API: http://localhost:5001")

if __name__ == '__main__':
    main()
