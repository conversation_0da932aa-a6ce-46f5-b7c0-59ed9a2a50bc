import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { buildApiUrl, API_ENDPOINTS } from '../config/api.js'

export const useQuotationStore = defineStore('quotation', () => {
  // 状态
  const opportunities = ref([])
  const cities = ref([])
  const positions = ref([])
  const quotationRecords = ref([])
  const loading = ref(false)
  const currentQuotation = ref(null)

  // 计算属性
  const totalRecords = computed(() => quotationRecords.value.length)
  const totalAmount = computed(() => 
    quotationRecords.value.reduce((sum, record) => sum + record.total_amount, 0)
  )

  // API调用方法
  const fetchOpportunities = async () => {
    try {
      loading.value = true
      const response = await axios.get(buildApiUrl(API_ENDPOINTS.opportunities))
      opportunities.value = response.data
    } catch (error) {
      console.error('获取商机数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchCities = async () => {
    try {
      loading.value = true
      const response = await axios.get(buildApiUrl(API_ENDPOINTS.cities))
      cities.value = response.data
    } catch (error) {
      console.error('获取城市数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchPositions = async () => {
    try {
      loading.value = true
      const response = await axios.get(buildApiUrl(API_ENDPOINTS.positions))
      positions.value = response.data
    } catch (error) {
      console.error('获取岗位数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchQuotationRecords = async () => {
    try {
      loading.value = true
      const response = await axios.get(buildApiUrl(API_ENDPOINTS.quotationRecords))
      quotationRecords.value = response.data
    } catch (error) {
      console.error('获取报价记录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addOpportunity = async (opportunityData) => {
    try {
      loading.value = true
      const response = await axios.post(buildApiUrl(API_ENDPOINTS.opportunities), opportunityData)
      if (response.data.success) {
        await fetchOpportunities()
      }
      return response.data
    } catch (error) {
      console.error('添加商机失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addCity = async (cityData) => {
    try {
      loading.value = true
      const response = await axios.post(buildApiUrl(API_ENDPOINTS.cities), cityData)
      if (response.data.success) {
        await fetchCities()
      }
      return response.data
    } catch (error) {
      console.error('添加城市失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateCity = async (cityId, cityData) => {
    try {
      loading.value = true
      const response = await axios.put(buildApiUrl(`${API_ENDPOINTS.cities}/${cityId}`), cityData)
      if (response.data.success) {
        await fetchCities()
      }
      return response.data
    } catch (error) {
      console.error('更新城市失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addPosition = async (positionData) => {
    try {
      loading.value = true
      const response = await axios.post(buildApiUrl(API_ENDPOINTS.positions), positionData)
      if (response.data.success) {
        await fetchPositions()
      }
      return response.data
    } catch (error) {
      console.error('添加岗位失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const generateQuotation = async (quotationData) => {
    try {
      loading.value = true
      const response = await axios.post(buildApiUrl(API_ENDPOINTS.generateQuotation), quotationData)
      if (response.data.success) {
        currentQuotation.value = response.data
        await fetchQuotationRecords()
      }
      return response.data
    } catch (error) {
      console.error('生成报价失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const getQuotationDetail = async (quotationId) => {
    try {
      loading.value = true
      const response = await axios.get(buildApiUrl(`${API_ENDPOINTS.quotationDetail}/${quotationId}`))
      return response.data
    } catch (error) {
      console.error('获取报价详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const importCities = async (file) => {
    try {
      loading.value = true
      const formData = new FormData()
      formData.append('file', file)
      const response = await axios.post(buildApiUrl(API_ENDPOINTS.importCities), formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (response.data.success) {
        await fetchCities()
      }
      return response.data
    } catch (error) {
      console.error('导入城市数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const exportQuotation = (quotationId) => {
    window.open(buildApiUrl(`${API_ENDPOINTS.exportQuotation}/${quotationId}`), '_blank')
  }

  const exportAllQuotations = () => {
    window.open(buildApiUrl(API_ENDPOINTS.exportAllQuotations), '_blank')
  }

  // 删除方法
  const deleteOpportunity = async (id) => {
    try {
      const response = await axios.delete(buildApiUrl(`${API_ENDPOINTS.opportunities}/${id}`))
      return response.data
    } catch (error) {
      console.error('删除商机失败:', error)
      throw error
    }
  }

  const deleteCity = async (id) => {
    try {
      const response = await axios.delete(buildApiUrl(`${API_ENDPOINTS.cities}/${id}`))
      return response.data
    } catch (error) {
      console.error('删除城市失败:', error)
      throw error
    }
  }

  const deletePosition = async (id) => {
    try {
      const response = await axios.delete(buildApiUrl(`${API_ENDPOINTS.positions}/${id}`))
      return response.data
    } catch (error) {
      console.error('删除岗位失败:', error)
      throw error
    }
  }

  // 初始化数据
  const initializeData = async () => {
    try {
      await Promise.all([
        fetchOpportunities(),
        fetchCities(),
        fetchPositions(),
        fetchQuotationRecords()
      ])
    } catch (error) {
      console.error('初始化数据失败:', error)
    }
  }

  return {
    // 状态
    opportunities,
    cities,
    positions,
    quotationRecords,
    loading,
    currentQuotation,
    
    // 计算属性
    totalRecords,
    totalAmount,
    
    // 方法
    fetchOpportunities,
    fetchCities,
    fetchPositions,
    fetchQuotationRecords,
    addOpportunity,
    addCity,
    updateCity,
    addPosition,
    generateQuotation,
    getQuotationDetail,
    importCities,
    exportQuotation,
    exportAllQuotations,
    deleteOpportunity,
    deleteCity,
    deletePosition,
    initializeData
  }
})
