<template>
  <div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold gradient-text mb-4">数据管理中心</h1>
        <p class="text-xl text-white/70">管理商机、社保城市、岗位薪资等基础数据</p>
      </div>

      <!-- 标签页导航 -->
      <div class="flex flex-wrap justify-center mb-8">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          @click="activeTab = tab.key"
          class="px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300"
          :class="activeTab === tab.key 
            ? 'btn-gemini' 
            : 'glass-card text-white/70 hover:text-white hover:bg-white/10'"
        >
          <component :is="tab.icon" class="w-5 h-5 mr-2 inline" />
          {{ tab.name }}
        </button>
      </div>

      <!-- 商机管理 -->
      <div v-if="activeTab === 'opportunities'" class="glass-card p-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-semibold text-white">商机管理</h2>
          <button @click="showAddOpportunityModal = true" class="btn-gemini">
            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            添加商机
          </button>
        </div>

        <div v-if="opportunities.length" class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="border-b border-white/20">
                <th class="text-left py-3 px-4 text-white/70">ID</th>
                <th class="text-left py-3 px-4 text-white/70">商机名称</th>
                <th class="text-left py-3 px-4 text-white/70">描述</th>
                <th class="text-left py-3 px-4 text-white/70">创建时间</th>
                <th class="text-left py-3 px-4 text-white/70">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="opportunity in opportunities" :key="opportunity.id" 
                  class="border-b border-white/10 hover:bg-white/5 transition-colors">
                <td class="py-3 px-4 text-white">{{ opportunity.id }}</td>
                <td class="py-3 px-4 text-white font-medium">{{ opportunity.name }}</td>
                <td class="py-3 px-4 text-white/70">{{ opportunity.description || '-' }}</td>
                <td class="py-3 px-4 text-white/70">{{ formatDate(opportunity.created_at) }}</td>
                <td class="py-3 px-4">
                  <div class="flex space-x-2">
                    <button
                      @click="editOpportunity(opportunity)"
                      class="text-blue-400 hover:text-blue-300 transition-colors"
                      title="编辑"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                      </svg>
                    </button>
                    <button
                      @click="deleteOpportunity(opportunity)"
                      class="text-red-400 hover:text-red-300 transition-colors"
                      title="删除"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-white/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
          <p class="text-white/50 mb-4">暂无商机数据</p>
          <button @click="showAddOpportunityModal = true" class="btn-gemini">
            添加第一个商机
          </button>
        </div>
      </div>

      <!-- 城市管理 -->
      <div v-if="activeTab === 'cities'" class="glass-card p-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-semibold text-white">城市社保公积金管理</h2>
          <div class="flex space-x-3">
            <button @click="showImportModal = true" class="glass-card px-4 py-2 text-white hover:bg-white/10 transition-all">
              <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
              </svg>
              导入Excel
            </button>
            <button @click="showAddCityModal = true" class="btn-gemini">
              <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              添加城市
            </button>
          </div>
        </div>

        <div v-if="cities.length" class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="border-b border-white/20">
                <th class="text-left py-3 px-4 text-white/70">城市名称</th>
                <th class="text-left py-3 px-4 text-white/70">社保基数</th>
                <th class="text-left py-3 px-4 text-white/70">社保比例</th>
                <th class="text-left py-3 px-4 text-white/70">公积金基数</th>
                <th class="text-left py-3 px-4 text-white/70">公积金比例</th>
                <th class="text-left py-3 px-4 text-white/70">公司缴纳总额</th>
                <th class="text-left py-3 px-4 text-white/70">更新时间</th>
                <th class="text-left py-3 px-4 text-white/70">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="city in cities" :key="city.id"
                  class="border-b border-white/10 hover:bg-white/5 transition-colors">
                <td class="py-3 px-4 text-white font-medium">{{ city.city_name }}</td>
                <td class="py-3 px-4 text-white">¥{{ city.social_base.toFixed(2) }}</td>
                <td class="py-3 px-4 text-white">{{ (city.social_rate * 100).toFixed(2) }}%</td>
                <td class="py-3 px-4 text-white">¥{{ city.housing_base.toFixed(2) }}</td>
                <td class="py-3 px-4 text-white">{{ (city.housing_rate * 100).toFixed(2) }}%</td>
                <td class="py-3 px-4 text-white font-semibold">¥{{ city.company_amount.toFixed(2) }}</td>
                <td class="py-3 px-4 text-white/70">{{ formatDate(city.updated_at) }}</td>
                <td class="py-3 px-4">
                  <div class="flex space-x-2">
                    <button
                      @click="editCity(city)"
                      class="text-blue-400 hover:text-blue-300 transition-colors"
                      title="编辑"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                      </svg>
                    </button>
                    <button
                      @click="deleteCity(city)"
                      class="text-red-400 hover:text-red-300 transition-colors"
                      title="删除"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-white/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
          </svg>
          <p class="text-white/50 mb-4">暂无城市数据</p>
          <div class="flex justify-center space-x-3">
            <button @click="showImportModal = true" class="glass-card px-4 py-2 text-white hover:bg-white/10 transition-all">
              导入Excel
            </button>
            <button @click="showAddCityModal = true" class="btn-gemini">
              添加第一个城市
            </button>
          </div>
        </div>
      </div>

      <!-- 岗位管理 -->
      <div v-if="activeTab === 'positions'" class="glass-card p-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-semibold text-white">岗位成本管理</h2>
          <button @click="showAddPositionModal = true" class="btn-gemini">
            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            添加岗位
          </button>
        </div>

        <!-- 筛选折叠按钮 -->
        <div class="flex items-center mb-2">
          <button @click="showFilters = !showFilters" class="flex items-center text-white/80 hover:text-white transition-all">
            <svg :class="showFilters ? 'rotate-0' : '-rotate-90'" class="w-5 h-5 mr-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
            <span>{{ showFilters ? '收起筛选' : '展开筛选' }}</span>
          </button>
        </div>
        <!-- 筛选区域 -->
        <div v-show="showFilters" :key="showFilters" class="glass border border-white/10 rounded-lg p-4 mb-6">
          <h3 class="text-lg font-semibold text-white mb-4">筛选条件</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label class="block text-sm font-medium text-white/70 mb-2">岗位名称</label>
              <input
                v-model="positionFilters.positionName"
                type="text"
                class="input-gemini w-full"
                placeholder="请输入岗位名称"
                @input="handleFilterChange"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-white/70 mb-2">岗位级别</label>
              <select
                v-model="positionFilters.positionLevel"
                class="select-gemini w-full"
                @change="handleFilterChange"
              >
                <option value="">全部级别</option>
                <option value="初级">初级</option>
                <option value="中级">中级</option>
                <option value="高级">高级</option>
                <option value="专家">专家</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-white/70 mb-2">城市</label>
              <select
                v-model="positionFilters.cityName"
                class="select-gemini w-full"
                @change="handleFilterChange"
              >
                <option value="">全部城市</option>
                <option v-for="city in cities" :key="city.id" :value="city.city_name">
                  {{ city.city_name }}
                </option>
              </select>
            </div>
          </div>
          <div class="flex justify-between items-center">
            <div class="text-sm text-white/70">
              共找到 {{ filteredPositions.length }} 条记录
            </div>
            <button
              @click="clearFilters"
              class="glass-card px-4 py-2 text-white hover:bg-white/10 transition-all text-sm"
            >
              清除筛选
            </button>
          </div>
        </div>

        <div v-if="filteredPositions.length" class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="border-b border-white/20">
                <th class="text-left py-3 px-4 text-white/70">岗位名称</th>
                <th class="text-left py-3 px-4 text-white/70">城市</th>
                <th class="text-left py-3 px-4 text-white/70">岗位级别</th>
                <th class="text-left py-3 px-4 text-white/70">月工资</th>
                <th class="text-left py-3 px-4 text-white/70">日工资</th>
                <th class="text-left py-3 px-4 text-white/70">更新时间</th>
                <th class="text-left py-3 px-4 text-white/70">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="position in filteredPositions" :key="position.id"
                  class="border-b border-white/10 hover:bg-white/5 transition-colors">
                <td class="py-3 px-4 text-white font-medium">{{ position.position_name }}</td>
                <td class="py-3 px-4 text-white">{{ position.city_name }}</td>
                <td class="py-3 px-4 text-white">
                  <span class="px-2 py-1 rounded-full text-xs font-medium"
                        :class="{
                          'bg-green-500/20 text-green-300': position.position_level === '初级',
                          'bg-blue-500/20 text-blue-300': position.position_level === '中级',
                          'bg-purple-500/20 text-purple-300': position.position_level === '高级',
                          'bg-orange-500/20 text-orange-300': position.position_level === '专家'
                        }">
                    {{ position.position_level || '未设置' }}
                  </span>
                </td>
                <td class="py-3 px-4 text-white">¥{{ position.monthly_salary.toFixed(2) }}</td>
                <td class="py-3 px-4 text-white">¥{{ position.daily_salary.toFixed(2) }}</td>
                <td class="py-3 px-4 text-white/70">{{ formatDate(position.updated_at) }}</td>
                <td class="py-3 px-4">
                  <div class="flex space-x-2">
                    <button
                      @click="editPosition(position)"
                      class="text-blue-400 hover:text-blue-300 transition-colors"
                      title="编辑"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                      </svg>
                    </button>
                    <button
                      @click="deletePosition(position)"
                      class="text-red-400 hover:text-red-300 transition-colors"
                      title="删除"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页组件 -->
        <div v-if="filteredPositions.length > 0 && positionsTotal > positionsPagination.pageSize" class="mt-6 flex justify-center">
          <div class="flex items-center space-x-2">
            <button
              @click="loadPositionsPage(positionsPagination.page - 1)"
              :disabled="positionsPagination.page <= 1"
              class="glass-card px-3 py-2 text-white hover:bg-white/10 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>

            <div class="flex items-center space-x-1">
              <button
                v-for="page in getPageNumbers()"
                :key="page"
                @click="loadPositionsPage(page)"
                class="px-3 py-2 text-sm transition-all"
                :class="page === positionsPagination.page
                  ? 'btn-gemini'
                  : 'glass-card text-white hover:bg-white/10'"
              >
                {{ page }}
              </button>
            </div>

            <button
              @click="loadPositionsPage(positionsPagination.page + 1)"
              :disabled="positionsPagination.page >= Math.ceil(positionsTotal / positionsPagination.pageSize)"
              class="glass-card px-3 py-2 text-white hover:bg-white/10 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>

            <span class="text-white/70 text-sm ml-4">
              共 {{ positionsTotal }} 条，第 {{ positionsPagination.page }} / {{ Math.ceil(positionsTotal / positionsPagination.pageSize) }} 页
            </span>
          </div>
        </div>

        <div v-else-if="filteredPositions.length === 0" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-white/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
          </svg>
          <p class="text-white/50 mb-4">
            {{ positions.length === 0 ? '暂无岗位数据' : '没有找到匹配的岗位数据' }}
          </p>
          <button @click="showAddPositionModal = true" class="btn-gemini">
            {{ positions.length === 0 ? '添加第一个岗位' : '添加岗位' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 模态框组件 -->
    <AddOpportunityModal 
      v-if="showAddOpportunityModal" 
      @close="showAddOpportunityModal = false"
      @success="handleAddSuccess"
    />
    
    <AddCityModal 
      v-if="showAddCityModal" 
      :visible="showAddCityModal"
      @close="showAddCityModal = false"
      @success="handleAddSuccess"
    />
    
    <EditCityModal 
      v-if="showEditCityModal" 
      :visible="showEditCityModal"
      :city="editingCity"
      @close="showEditCityModal = false"
      @success="handleAddSuccess"
    />
    
    <AddPositionModal
      v-if="showAddPositionModal"
      @close="showAddPositionModal = false"
      @success="handleAddSuccess"
    />

    <!-- 编辑岗位模态框 -->
    <EditPositionModal
      v-if="showEditPositionModal && editingPosition"
      :position="editingPosition"
      @close="showEditPositionModal = false"
      @success="handleEditSuccess"
    />

    <ImportModal
      v-if="showImportModal"
      @close="showImportModal = false"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'
import AddOpportunityModal from '../components/AddOpportunityModal.vue'
import AddCityModal from '../components/AddCityModal.vue'
import EditCityModal from '../components/EditCityModal.vue'
import AddPositionModal from '../components/AddPositionModal.vue'
import EditPositionModal from '../components/EditPositionModal.vue'
import ImportModal from '../components/ImportModal.vue'
import Icons from '../components/Icons.vue'

export default {
  name: 'DataManagement',
  components: {
    AddOpportunityModal,
    AddCityModal,
    EditCityModal,
    AddPositionModal,
    EditPositionModal,
    ImportModal,
    ...Icons
  },
  setup() {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()

    // 响应式数据
    const activeTab = ref('opportunities')
    const showAddOpportunityModal = ref(false)
    const showAddCityModal = ref(false)
    const showEditCityModal = ref(false)
    const showAddPositionModal = ref(false)
    const showEditPositionModal = ref(false)
    const showImportModal = ref(false)
    const editingCity = ref(null)
    const editingPosition = ref(null)

    // 标签页配置
    const tabs = [
      {
        key: 'opportunities',
        name: '商机管理',
        icon: 'BriefcaseIcon'
      },
      {
        key: 'cities',
        name: '城市数据',
        icon: 'BuildingOfficeIcon'
      },
      {
        key: 'positions',
        name: '岗位成本',
        icon: 'UserGroupIcon'
      }
    ]

    // 分页状态
    const positionsPagination = ref({
      page: 1,
      pageSize: 10,
      total: 0
    })

    // 筛选状态
    const showFilters = ref(true)
    const positionFilters = ref({
      positionName: '',
      positionLevel: '',
      cityName: ''
    })

    // 计算属性
    const opportunities = computed(() => quotationStore.opportunities)
    const cities = computed(() => quotationStore.cities)
    const positions = computed(() => quotationStore.positions)
    const positionsTotal = computed(() => quotationStore.positionsTotal || 0)

    // 筛选后的岗位列表（直接使用后端数据）
    const filteredPositions = computed(() => positions.value)

    // 方法
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const handleAddSuccess = async () => {
      try {
        await quotationStore.initializeData()
        notificationStore.success('数据添加成功！')
      } catch (error) {
        console.error('刷新数据失败:', error)
      }
    }

    const handleEditSuccess = async () => {
      try {
        await quotationStore.initializeData()
        notificationStore.success('数据更新成功！')
      } catch (error) {
        console.error('刷新数据失败:', error)
      }
    }

    const handleImportSuccess = async () => {
      try {
        await quotationStore.fetchCities()
        notificationStore.success('数据导入成功！')
      } catch (error) {
        console.error('刷新数据失败:', error)
      }
    }

    const loadData = async () => {
      try {
        await quotationStore.initializeData()
      } catch (error) {
        console.error('加载数据失败:', error)
        notificationStore.error('加载数据失败')
      }
    }

    // 编辑和删除方法
    const editOpportunity = (opportunity) => {
      // TODO: 实现编辑商机功能
      notificationStore.info('编辑功能开发中...')
    }

    const deleteOpportunity = async (opportunity) => {
      if (!confirm(`确定要删除商机"${opportunity.name}"吗？`)) return

      try {
        await quotationStore.deleteOpportunity(opportunity.id)
        notificationStore.success('商机删除成功')
        await loadData()
      } catch (error) {
        console.error('删除商机失败:', error)
        notificationStore.error('删除商机失败')
      }
    }

    const editCity = (city) => {
      editingCity.value = city
      showEditCityModal.value = true
    }

    const deleteCity = async (city) => {
      if (!confirm(`确定要删除城市"${city.city_name}"吗？`)) return

      try {
        await quotationStore.deleteCity(city.id)
        notificationStore.success('城市删除成功')
        await loadData()
      } catch (error) {
        console.error('删除城市失败:', error)
        notificationStore.error('删除城市失败')
      }
    }

    const editPosition = (position) => {
      editingPosition.value = position
      showEditPositionModal.value = true
    }

    const deletePosition = async (position) => {
      if (!confirm(`确定要删除岗位"${position.position_name} (${position.city_name})"吗？`)) return

      try {
        await quotationStore.deletePosition(position.id)
        notificationStore.success('岗位删除成功')
        // 重新获取数据
        await loadFilteredPositions()
        // 计算最大页码
        const maxPage = Math.max(1, Math.ceil(positionsTotal.value / positionsPagination.value.pageSize))
        // 如果当前页大于最大页码，或当前页数据为空且不是第一页，则跳转到第一页
        if (positionsPagination.value.page > maxPage || (filteredPositions.value.length === 0 && positionsPagination.value.page > 1)) {
          positionsPagination.value.page = 1
          await loadFilteredPositions()
        }
      } catch (error) {
        console.error('删除岗位失败:', error)
        notificationStore.error('删除岗位失败')
      }
    }

    // 分页相关方法
    const loadPositionsPage = async (page) => {
      if (page < 1) return
      positionsPagination.value.page = page
      try {
        await quotationStore.fetchPositions(
          page, 
          positionsPagination.value.pageSize, 
          positionFilters.value
        )
        positionsPagination.value.total = quotationStore.positionsTotal || 0
      } catch (error) {
        console.error('加载岗位页面失败:', error)
        notificationStore.error('加载岗位页面失败')
      }
    }

    const getPageNumbers = () => {
      const total = Math.ceil(positionsTotal.value / positionsPagination.value.pageSize)
      const current = positionsPagination.value.page
      const pages = []
      // 显示当前页前后2页
      const start = Math.max(1, current - 2)
      const end = Math.min(total, current + 2)
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    }

    // 筛选相关方法
    const handleFilterChange = async () => {
      // 筛选变化时重置分页并重新加载数据
      positionsPagination.value.page = 1
      await loadFilteredPositions()
    }

    const clearFilters = async () => {
      positionFilters.value = {
        positionName: '',
        positionLevel: '',
        cityName: ''
      }
      positionsPagination.value.page = 1
      await loadFilteredPositions()
    }

    const loadFilteredPositions = async () => {
      try {
        await quotationStore.fetchPositions(
          positionsPagination.value.page, 
          positionsPagination.value.pageSize, 
          positionFilters.value
        )
        positionsPagination.value.total = quotationStore.positionsTotal || 0
      } catch (error) {
        console.error('加载筛选数据失败:', error)
        notificationStore.error('加载筛选数据失败')
      }
    }

    // 生命周期
    onMounted(() => {
      loadData()
    })

    return {
      activeTab,
      tabs,
      opportunities,
      cities,
      positions,
      filteredPositions,
      positionFilters,
      showFilters,
      showAddOpportunityModal,
      showAddCityModal,
      showEditCityModal,
      editingCity,
      showAddPositionModal,
      showEditPositionModal,
      editingPosition,
      showImportModal,
      formatDate,
      handleAddSuccess,
      handleEditSuccess,
      handleImportSuccess,
      editOpportunity,
      deleteOpportunity,
      editCity,
      deleteCity,
      editPosition,
      deletePosition,
      positionsPagination,
      positionsTotal,
      loadPositionsPage,
      getPageNumbers,
      handleFilterChange,
      clearFilters,
      loadFilteredPositions
    }
  }
}
</script>

<style scoped>
/* 表格悬停效果 */
tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 标签页切换动画 */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
