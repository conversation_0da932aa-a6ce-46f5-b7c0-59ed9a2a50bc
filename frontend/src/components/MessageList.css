.message-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
}

.message-wrapper {
  display: flex;
  width: 100%;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  justify-content: flex-end;
}

.assistant-message {
  justify-content: flex-start;
}

.message-content {
  display: flex;
  max-width: 80%;
  gap: 12px;
  align-items: flex-start;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 4px;
}

.user-avatar {
  background: linear-gradient(135deg, #4285f4, #1a73e8);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.assistant-avatar {
  background: linear-gradient(135deg, #34a853, #137333);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.message-body {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 4px;
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-sender {
  font-weight: 600;
  font-size: 12px;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message-time {
  font-size: 11px;
  color: #666666;
}

.message-card {
  border-radius: 18px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.user-card {
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.2), rgba(26, 115, 232, 0.2)) !important;
  border-color: rgba(66, 133, 244, 0.3) !important;
}

.assistant-card {
  background: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.message-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.message-card .ant-card-body {
  padding: 16px !important;
}

.message-text {
  color: #ffffff;
  line-height: 1.6;
  word-wrap: break-word;
}

/* Markdown 样式 */
.markdown-content {
  color: #ffffff;
}

.markdown-paragraph {
  margin-bottom: 12px;
  line-height: 1.6;
}

.markdown-paragraph:last-child {
  margin-bottom: 0;
}

.markdown-heading {
  color: #ffffff;
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.markdown-list {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-list-item {
  margin: 4px 0;
  color: #ffffff;
}

.markdown-blockquote {
  border-left: 4px solid rgba(66, 133, 244, 0.5);
  margin: 12px 0;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-style: italic;
}

.inline-code {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ff6b6b !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.code-block {
  margin: 12px 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.code-block pre {
  margin: 0 !important;
  background: rgba(0, 0, 0, 0.3) !important;
  padding: 16px !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
}

/* 打字指示器 */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
}

.typing-indicator span {
  height: 4px;
  width: 4px;
  background: rgba(66, 133, 244, 0.7);
  border-radius: 50%;
  display: inline-block;
  margin: 0 1px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-content {
    max-width: 95%;
  }
  
  .message-header {
    margin-bottom: 6px;
  }
  
  .message-card .ant-card-body {
    padding: 12px !important;
  }
  
  .code-block pre {
    padding: 12px !important;
    font-size: 12px !important;
  }
}
