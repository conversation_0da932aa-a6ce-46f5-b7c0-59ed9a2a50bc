# 🚀 AI商机报价系统 - 部署指南

## 系统概述

这是一个采用Gemini风格设计的AI驱动商机报价系统，具有炫酷的界面效果和强大的功能。

### 核心特性
- 🎨 **Gemini风格界面**：玻璃态设计、渐变效果、粒子背景
- 🔄 **双计算模式**：按月/按天两种计算方式
- 📊 **智能报价**：自动计算社保公积金成本
- 📱 **响应式设计**：完美适配各种设备
- 💾 **数据管理**：Excel导入导出、历史记录

## 技术栈

### 后端
- **Flask 2.3.3**：轻量级Web框架
- **SQLAlchemy**：ORM数据库操作
- **pandas + openpyxl**：Excel处理
- **SQLite**：轻量级数据库

### 前端
- **Vue.js 3**：现代化前端框架
- **Vite**：快速构建工具
- **Tailwind CSS**：原子化CSS框架
- **Pinia**：状态管理
- **particles.js**：粒子效果

## 部署步骤

### 1. 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 2. 克隆项目
```bash
git clone <repository-url>
cd price
```

### 3. 后端部署
```bash
# 安装Python依赖
pip install -r requirements.txt

# 初始化数据库
python init_db.py

# 启动后端服务
python app.py
```

### 4. 前端部署
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 开发模式启动
npm run dev

# 或生产构建
npm run build
```

### 5. 访问系统
- 前端开发服务器：http://localhost:3000
- 后端API服务器：http://localhost:5000

## 功能说明

### 数据管理
1. **商机管理**：录入和管理商机信息
2. **城市数据**：管理各城市社保公积金标准
3. **岗位成本**：维护不同岗位的工资标准

### 报价生成
1. **参数设置**：选择计算类型、工期、城市、岗位
2. **智能计算**：自动计算各项成本
3. **结果展示**：炫酷的报价明细表
4. **Excel导出**：一键导出专业报表

### 记录管理
1. **历史查看**：查看所有报价记录
2. **详情展示**：查看报价详细信息
3. **批量导出**：导出所有记录

## 计算逻辑

### 按月计算
```
月度总成本 = 月工资 + 个人社保公积金 + 公司社保公积金
工期小计 = 月度总成本 × 工期（月）
```

### 按天计算
```
月度总成本 = 日工资 × 考勤基数 + 公司社保公积金
工期小计 = 月度总成本 × 工期（天）
```

### 总计算
```
总报价 = 所有城市-岗位组合的小计之和
```

## 界面特色

### Gemini风格设计
- **玻璃态效果**：半透明背景，模糊边框
- **渐变色彩**：蓝紫粉橙绿的渐变组合
- **动态粒子**：背景粒子连线效果
- **流畅动画**：悬停、点击、切换动画
- **现代字体**：Inter字体，清晰易读

### 交互体验
- **响应式布局**：适配手机、平板、桌面
- **智能提示**：表单验证、操作反馈
- **快捷操作**：拖拽上传、批量选择
- **实时预览**：数据实时计算显示

## 生产部署建议

### 后端部署
```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 前端部署
```bash
# 构建生产版本
cd frontend
npm run build

# 使用nginx或其他静态服务器部署dist目录
```

### 数据库
- 开发环境：SQLite
- 生产环境：建议使用PostgreSQL或MySQL

### 反向代理配置（Nginx）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API
    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 维护说明

### 数据备份
```bash
# 备份SQLite数据库
cp quotation_system.db quotation_system_backup_$(date +%Y%m%d).db
```

### 日志监控
- 后端日志：Flask应用日志
- 前端日志：浏览器控制台
- 错误监控：建议集成Sentry等工具

### 性能优化
- 前端：代码分割、懒加载、CDN
- 后端：数据库索引、缓存、连接池
- 网络：Gzip压缩、静态资源缓存

## 故障排除

### 常见问题
1. **数据库连接失败**：检查数据库文件权限
2. **前端无法访问后端**：检查CORS配置
3. **Excel导入失败**：检查文件格式和列名
4. **粒子效果不显示**：检查particles.js加载

### 调试模式
```bash
# 后端调试
export FLASK_DEBUG=1
python app.py

# 前端调试
npm run dev
```

## 联系支持

如有问题或建议，请联系开发团队。

---

🎉 享受使用这个炫酷的AI报价系统吧！
