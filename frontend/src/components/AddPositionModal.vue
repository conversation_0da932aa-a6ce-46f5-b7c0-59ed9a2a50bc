<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative glass-card p-6 w-full max-w-md">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-semibold text-white">添加岗位</h3>
        <button @click="$emit('close')" class="text-white/70 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            岗位名称 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.positionName"
            type="text"
            class="input-gemini w-full"
            placeholder="请输入岗位名称"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            所属城市 <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.cityName"
            class="select-gemini w-full"
            required
          >
            <option value="">请选择城市</option>
            <option v-for="city in cities" :key="city.id" :value="city.city_name">
              {{ city.city_name }}
            </option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            岗位级别 <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.positionLevel"
            class="select-gemini w-full"
            required
          >
            <option value="">请选择级别</option>
            <option value="初级">初级</option>
            <option value="中级">中级</option>
            <option value="高级">高级</option>
            <option value="专家">专家</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-white mb-2">
            月工资 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.monthlySalary"
            type="number"
            step="0.01"
            min="0"
            class="input-gemini w-full"
            placeholder="请输入月工资"
            required
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-white mb-2">
            日工资 <span class="text-red-400">*</span>
          </label>
          <div class="flex space-x-2">
            <input
              v-model="form.dailySalary"
              type="number"
              step="0.01"
              min="0"
              class="input-gemini flex-1"
              placeholder="请输入日工资"
              required
            />
            <button
              type="button"
              @click="calculateDailySalary"
              class="glass-card px-3 py-2 text-white hover:bg-white/10 transition-all text-sm"
              title="根据月工资自动计算"
            >
              自动计算
            </button>
          </div>
        </div>

        <!-- 预览 -->
        <div class="glass border border-white/10 rounded-lg p-3">
          <div class="text-sm text-white/70 mb-2">预览</div>
          <div class="text-sm text-white">
            <div>岗位：{{ form.positionName || '未填写' }} ({{ form.cityName || '未选择' }})</div>
            <div>级别：{{ form.positionLevel || '未选择' }}</div>
            <div>月薪：¥{{ parseFloat(form.monthlySalary || 0).toFixed(2) }}</div>
            <div>日薪：¥{{ parseFloat(form.dailySalary || 0).toFixed(2) }}</div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="glass-card px-6 py-2 text-white hover:bg-white/10 transition-all"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="!canSubmit || loading"
            class="btn-gemini px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'

export default {
  name: 'AddPositionModal',
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()
    
    const form = ref({
      positionName: '',
      cityName: '',
      positionLevel: '',
      monthlySalary: '',
      dailySalary: ''
    })
    
    const loading = ref(false)
    const cities = computed(() => quotationStore.cities)
    
    const canSubmit = computed(() => {
      return form.value.positionName.trim() &&
             form.value.cityName.trim() &&
             form.value.positionLevel.trim() &&
             form.value.monthlySalary !== '' &&
             form.value.dailySalary !== '' &&
             parseFloat(form.value.monthlySalary) >= 0 &&
             parseFloat(form.value.dailySalary) >= 0
    })
    
    const calculateDailySalary = () => {
      if (form.value.monthlySalary) {
        const monthly = parseFloat(form.value.monthlySalary)
        const daily = (monthly / 22).toFixed(2) // 按22个工作日计算
        form.value.dailySalary = daily
      }
    }
    
    const handleSubmit = async () => {
      if (!canSubmit.value) return
      
      try {
        loading.value = true
        const result = await quotationStore.addPosition({
          position_name: form.value.positionName.trim(),
          city_name: form.value.cityName.trim(),
          position_level: form.value.positionLevel.trim(),
          monthly_salary: parseFloat(form.value.monthlySalary),
          daily_salary: parseFloat(form.value.dailySalary)
        })
        
        if (result.success) {
          notificationStore.success('岗位添加成功')
          emit('success')
          emit('close')
        } else {
          notificationStore.error(result.message || '岗位添加失败')
        }
      } catch (error) {
        console.error('添加岗位失败:', error)
        notificationStore.error('岗位添加失败')
      } finally {
        loading.value = false
      }
    }
    
    onMounted(async () => {
      if (cities.value.length === 0) {
        await quotationStore.fetchCities()
      }
    })
    
    return {
      form,
      cities,
      loading,
      canSubmit,
      calculateDailySalary,
      handleSubmit
    }
  }
}
</script>
