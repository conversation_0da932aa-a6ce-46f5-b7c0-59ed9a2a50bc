<template>
  <div id="app" class="min-h-screen relative">
    <!-- 导航栏 -->
    <nav class="fixed top-0 left-0 right-0 z-50 glass border-b border-white/10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <span class="text-xl font-bold gradient-text">AI报价系统</span>
          </div>

          <!-- 导航菜单 -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link 
              v-for="item in navItems" 
              :key="item.name"
              :to="item.path"
              class="nav-link"
              :class="{ 'active': $route.path === item.path }"
            >
              <component :is="item.icon" class="w-4 h-4 mr-2" />
              {{ item.name }}
            </router-link>
          </div>

          <!-- 移动端菜单按钮 -->
          <button 
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="md:hidden p-2 rounded-lg glass-card"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <div v-if="mobileMenuOpen" class="md:hidden glass border-t border-white/10">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <router-link 
            v-for="item in navItems" 
            :key="item.name"
            :to="item.path"
            @click="mobileMenuOpen = false"
            class="mobile-nav-link"
          >
            <component :is="item.icon" class="w-4 h-4 mr-3" />
            {{ item.name }}
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="pt-16 min-h-screen">
      <router-view />
    </main>

    <!-- 浮动操作按钮 -->
    <div class="fixed bottom-6 right-6 z-40">
      <button 
        @click="scrollToTop"
        class="w-12 h-12 rounded-full btn-gemini shadow-lg hover:shadow-xl transition-all duration-300"
        v-show="showScrollTop"
      >
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
      </button>
    </div>

    <!-- 全局通知 -->
    <div v-if="notification.show" class="fixed top-20 right-4 z-50 max-w-sm">
      <div class="glass-card p-4 shadow-lg" :class="notificationClass">
        <div class="flex items-center">
          <component :is="notification.icon" class="w-5 h-5 mr-3 flex-shrink-0" />
          <p class="text-sm font-medium">{{ notification.message }}</p>
          <button @click="hideNotification" class="ml-auto">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from './stores/notification'

export default {
  name: 'App',
  setup() {
    const router = useRouter()
    const notificationStore = useNotificationStore()
    
    const mobileMenuOpen = ref(false)
    const showScrollTop = ref(false)
    
    const navItems = [
      { name: '首页', path: '/', icon: 'HomeIcon' },
      { name: '数据管理', path: '/data', icon: 'DatabaseIcon' },
      { name: '生成报价', path: '/quotation', icon: 'CalculatorIcon' },
      { name: '报价记录', path: '/records', icon: 'DocumentTextIcon' }
    ]
    
    const notification = computed(() => notificationStore.notification)
    
    const notificationClass = computed(() => {
      const type = notification.value.type
      return {
        'border-green-500/50 bg-green-500/10': type === 'success',
        'border-red-500/50 bg-red-500/10': type === 'error',
        'border-yellow-500/50 bg-yellow-500/10': type === 'warning',
        'border-blue-500/50 bg-blue-500/10': type === 'info'
      }
    })
    
    const scrollToTop = () => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
    
    const handleScroll = () => {
      showScrollTop.value = window.scrollY > 300
    }
    
    const hideNotification = () => {
      notificationStore.hide()
    }
    
    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
    })
    
    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })
    
    return {
      mobileMenuOpen,
      showScrollTop,
      navItems,
      notification,
      notificationClass,
      scrollToTop,
      hideNotification
    }
  }
}
</script>

<style scoped>
.nav-link {
  @apply flex items-center px-3 py-2 rounded-lg text-sm font-medium text-white/70 hover:text-white hover:bg-white/10 transition-all duration-200;
}

.nav-link.active {
  @apply text-white bg-white/10;
}

.mobile-nav-link {
  @apply flex items-center px-3 py-2 rounded-lg text-base font-medium text-white/70 hover:text-white hover:bg-white/10 transition-all duration-200;
}
</style>
