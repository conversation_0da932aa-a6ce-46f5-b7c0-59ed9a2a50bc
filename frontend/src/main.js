import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import './styles/main.css'

// 导入页面组件
import Home from './pages/Home.vue'
import DataManagement from './pages/DataManagement.vue'
import QuotationGenerator from './pages/QuotationGenerator.vue'
import QuotationRecords from './pages/QuotationRecords.vue'

// 路由配置
const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/data', name: 'DataManagement', component: DataManagement },
  { path: '/quotation', name: 'QuotationGenerator', component: QuotationGenerator },
  { path: '/records', name: 'QuotationRecords', component: QuotationRecords }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 创建应用
const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 初始化粒子效果
const initParticles = () => {
  if (window.particlesJS) {
    window.particlesJS('particles-js', {
      particles: {
        number: {
          value: 80,
          density: {
            enable: true,
            value_area: 800
          }
        },
        color: {
          value: ['#4285f4', '#9c27b0', '#e91e63', '#ff9800', '#4caf50']
        },
        shape: {
          type: 'circle',
          stroke: {
            width: 0,
            color: '#000000'
          }
        },
        opacity: {
          value: 0.5,
          random: false,
          anim: {
            enable: false,
            speed: 1,
            opacity_min: 0.1,
            sync: false
          }
        },
        size: {
          value: 3,
          random: true,
          anim: {
            enable: false,
            speed: 40,
            size_min: 0.1,
            sync: false
          }
        },
        line_linked: {
          enable: true,
          distance: 150,
          color: '#4285f4',
          opacity: 0.4,
          width: 1
        },
        move: {
          enable: true,
          speed: 6,
          direction: 'none',
          random: false,
          straight: false,
          out_mode: 'out',
          bounce: false,
          attract: {
            enable: false,
            rotateX: 600,
            rotateY: 1200
          }
        }
      },
      interactivity: {
        detect_on: 'canvas',
        events: {
          onhover: {
            enable: true,
            mode: 'repulse'
          },
          onclick: {
            enable: true,
            mode: 'push'
          },
          resize: true
        },
        modes: {
          grab: {
            distance: 400,
            line_linked: {
              opacity: 1
            }
          },
          bubble: {
            distance: 400,
            size: 40,
            duration: 2,
            opacity: 8,
            speed: 3
          },
          repulse: {
            distance: 200,
            duration: 0.4
          },
          push: {
            particles_nb: 4
          },
          remove: {
            particles_nb: 2
          }
        }
      },
      retina_detect: true
    })
  }
}

// 加载粒子效果库
const loadParticlesJS = () => {
  return new Promise((resolve) => {
    if (window.particlesJS) {
      resolve()
      return
    }
    
    const script = document.createElement('script')
    script.src = 'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js'
    script.onload = resolve
    document.head.appendChild(script)
  })
}

// 应用启动
const startApp = async () => {
  try {
    // 加载粒子效果
    await loadParticlesJS()
    initParticles()
    
    // 隐藏加载屏幕
    setTimeout(() => {
      const loading = document.getElementById('loading')
      if (loading) {
        loading.style.opacity = '0'
        setTimeout(() => {
          loading.style.display = 'none'
        }, 500)
      }
    }, 1000)
    
    // 挂载应用
    app.mount('#app')
  } catch (error) {
    console.error('应用启动失败:', error)
    // 即使粒子效果加载失败，也要启动应用
    app.mount('#app')
  }
}

startApp()
