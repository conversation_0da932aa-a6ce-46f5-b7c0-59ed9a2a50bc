#!/usr/bin/env python3
"""
AutoGen Chat API 测试脚本
用于测试后端API的基本功能
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_sync_chat():
    """测试同步聊天接口"""
    print("\n💬 测试同步聊天接口...")
    try:
        payload = {
            "message": "Hello, this is a test message!",
            "conversation_id": "test_conv_123"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/chat",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 同步聊天成功:")
            print(f"   消息: {data.get('message', 'N/A')}")
            print(f"   对话ID: {data.get('conversation_id', 'N/A')}")
            return True
        else:
            print(f"❌ 同步聊天失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 同步聊天异常: {e}")
        return False

def test_stream_chat():
    """测试流式聊天接口"""
    print("\n🌊 测试流式聊天接口...")
    try:
        payload = {
            "message": "Tell me a short joke!",
            "conversation_id": "test_conv_stream"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/chat/stream",
            json=payload,
            headers={"Content-Type": "application/json"},
            stream=True
        )
        
        if response.status_code == 200:
            print("✅ 流式聊天连接成功，接收数据:")
            content_received = False
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        
                        if data_str == '[DONE]':
                            print("   📝 流式传输完成")
                            break
                        
                        try:
                            data = json.loads(data_str)
                            if data.get('content'):
                                content_received = True
                                print(f"   📨 接收内容: {data['content'][:50]}...")
                                if data.get('done'):
                                    print("   ✅ 消息接收完成")
                                    break
                        except json.JSONDecodeError:
                            print(f"   ⚠️  无法解析JSON: {data_str}")
            
            return content_received
        else:
            print(f"❌ 流式聊天失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 流式聊天异常: {e}")
        return False

def test_conversation_history():
    """测试对话历史接口"""
    print("\n📚 测试对话历史接口...")
    try:
        conversation_id = "test_conv_123"
        response = requests.get(f"{API_BASE_URL}/api/conversation/{conversation_id}/history")
        
        if response.status_code == 200:
            data = response.json()
            messages = data.get('messages', [])
            print(f"✅ 对话历史获取成功:")
            print(f"   对话ID: {data.get('conversation_id', 'N/A')}")
            print(f"   消息数量: {len(messages)}")
            for i, msg in enumerate(messages[:3]):  # 只显示前3条
                print(f"   消息{i+1}: {msg.get('role', 'N/A')} - {msg.get('content', 'N/A')[:30]}...")
            return True
        else:
            print(f"❌ 对话历史获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 对话历史异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 AutoGen Chat API")
    print("=" * 50)
    
    # 测试结果统计
    tests = [
        ("健康检查", test_health),
        ("同步聊天", test_sync_chat),
        ("流式聊天", test_stream_chat),
        ("对话历史", test_conversation_history),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # 测试间隔
        except KeyboardInterrupt:
            print("\n⏹️  测试被用户中断")
            break
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生未预期错误: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！API工作正常")
    else:
        print("⚠️  部分测试失败，请检查后端服务")

if __name__ == "__main__":
    main()
