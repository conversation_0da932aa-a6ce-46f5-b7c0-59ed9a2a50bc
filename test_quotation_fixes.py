#!/usr/bin/env python3
"""
测试报价功能修复的脚本
"""
import requests
import json

def test_quotation_fixes():
    """测试报价功能修复"""
    base_url = "http://localhost:5001"
    
    print("🔧 测试报价功能修复")
    print("=" * 50)
    
    # 1. 测试生成报价（不受城市限制）
    print("\n1. 测试生成报价功能（岗位选择不受城市限制）")
    
    # 创建一个测试报价，选择不同城市的岗位
    test_quotation = {
        "opportunity_name": "测试跨城市岗位报价",
        "calculation_type": "daily",
        "work_duration_days": 30,
        "quotation_coefficient": 1.2,
        "selected_cities": ["北京", "上海"],
        "selected_positions": [
            "软件开发工程师@北京@1",
            "UI设计师@上海@1",
            "项目经理@深圳@1"  # 注意：深圳不在选择的城市中
        ],
        "external_prices": [50000, 30000, 45000]
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate_quotation",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_quotation)
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 跨城市岗位报价生成成功")
                print(f"   - 报价ID: {result.get('quotation_id')}")
                print(f"   - 总金额: ¥{result.get('total_amount', 0):.2f}")
                print(f"   - 明细数量: {len(result.get('details', []))}")
                
                # 验证是否包含深圳的岗位
                details = result.get('details', [])
                cities_in_result = set(detail['city_name'] for detail in details)
                print(f"   - 包含城市: {', '.join(cities_in_result)}")
                
                if '深圳' in cities_in_result:
                    print(f"   ✅ 成功包含非选择城市的岗位（深圳）")
                else:
                    print(f"   ❌ 未包含非选择城市的岗位")
                
                return result.get('quotation_id')
            else:
                print(f"❌ 报价生成失败: {result.get('message')}")
                return None
        else:
            print(f"❌ 报价生成请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 报价生成异常: {e}")
        return None

def test_quotation_detail(quotation_id):
    """测试报价详情显示"""
    if not quotation_id:
        print("\n2. ❌ 跳过报价详情测试（没有有效的报价ID）")
        return
    
    base_url = "http://localhost:5001"
    print(f"\n2. 测试报价详情显示（ID: {quotation_id}）")
    
    try:
        response = requests.get(f"{base_url}/api/quotation_detail/{quotation_id}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 报价详情获取成功")
            
            # 检查详情数据结构
            details = result.get('details', [])
            if details:
                first_detail = details[0]
                expected_fields = [
                    'city_name', 'position_name', 'position_level', 'count',
                    'monthly_salary', 'daily_salary', 'company_insurance',
                    'subtotal', 'external_price'
                ]
                
                print(f"   - 明细数量: {len(details)}")
                print(f"   - 字段检查:")
                
                for field in expected_fields:
                    if field in first_detail:
                        print(f"     ✅ {field}: {first_detail[field]}")
                    else:
                        print(f"     ❌ 缺少字段: {field}")
                
                # 检查是否有岗位级别信息
                levels = set(detail.get('position_level') for detail in details if detail.get('position_level'))
                if levels:
                    print(f"   - 岗位级别: {', '.join(levels)}")
                else:
                    print(f"   ⚠️ 没有岗位级别信息")
                
                # 检查是否有人数信息
                counts = [detail.get('count', 1) for detail in details]
                print(f"   - 岗位人数: {counts}")
                
                # 检查是否有报价金额
                external_prices = [detail.get('external_price', 0) for detail in details]
                print(f"   - 报价金额: {[f'¥{price:.2f}' for price in external_prices]}")
                
            else:
                print(f"   ❌ 没有明细数据")
        else:
            print(f"❌ 报价详情获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 报价详情测试异常: {e}")

def test_frontend_access():
    """测试前端访问"""
    print("\n3. 测试前端访问")
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print(f"✅ 前端页面正常访问")
        else:
            print(f"❌ 前端页面访问异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端访问异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试报价功能修复...")
    
    quotation_id = test_quotation_fixes()
    test_quotation_detail(quotation_id)
    test_frontend_access()
    
    print("\n" + "=" * 50)
    print("✨ 报价功能修复测试完成！")
    print("\n📋 修复总结:")
    print("1. ✅ 岗位选择取消城市联动限制")
    print("   - 可以选择任意城市的岗位")
    print("   - 不再要求先选择城市")
    print("   - 城市筛选改为可选功能")
    
    print("\n2. ✅ 报价详情页面字段更新")
    print("   - 添加岗位级别列")
    print("   - 添加岗位人数列")
    print("   - 添加成本列")
    print("   - 添加报价金额列")
    print("   - 城市汇总显示成本和报价合计")
    
    print("\n🎯 功能改进:")
    print("- 岗位选择更加灵活，不受城市限制")
    print("- 报价详情与生成页面字段一致")
    print("- 支持跨城市岗位组合报价")
    print("- 详情页面信息更加完整")
    
    print("\n🌐 使用指南:")
    print("1. 访问: http://localhost:3000")
    print("2. 生成报价页面:")
    print("   - 可以先选择岗位，再选择城市")
    print("   - 岗位选择不受城市限制")
    print("   - 支持跨城市岗位组合")
    print("3. 报价记录页面:")
    print("   - 详情显示完整字段信息")
    print("   - 包含岗位级别、人数、成本、报价金额")
    print("   - 城市汇总显示成本和报价合计")

if __name__ == '__main__':
    main()
