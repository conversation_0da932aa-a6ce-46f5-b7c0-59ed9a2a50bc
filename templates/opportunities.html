{% extends "base.html" %}

{% block title %}商机管理 - 商机报价系统{% endblock %}

{% block content %}
<div id="alerts"></div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">商机管理</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOpportunityModal">
                    <i class="bi bi-plus"></i> 添加商机
                </button>
            </div>
            <div class="card-body">
                {% if opportunities %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>商机名称</th>
                                <th>描述</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for opportunity in opportunities %}
                            <tr>
                                <td>{{ opportunity.id }}</td>
                                <td>{{ opportunity.name }}</td>
                                <td>{{ opportunity.description or '-' }}</td>
                                <td>{{ opportunity.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无商机数据</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOpportunityModal">
                        添加第一个商机
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 添加商机模态框 -->
<div class="modal fade" id="addOpportunityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加商机</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="opportunityForm">
                    <div class="mb-3">
                        <label for="opportunityName" class="form-label">商机名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="opportunityName" required>
                    </div>
                    <div class="mb-3">
                        <label for="opportunityDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="opportunityDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addOpportunity()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addOpportunity() {
    const name = $('#opportunityName').val().trim();
    const description = $('#opportunityDescription').val().trim();
    
    if (!name) {
        showAlert('请输入商机名称', 'danger');
        return;
    }
    
    const data = {
        name: name,
        description: description
    };
    
    submitForm('/api/opportunities', data, function(response) {
        $('#addOpportunityModal').modal('hide');
        $('#opportunityForm')[0].reset();
        setTimeout(() => {
            location.reload();
        }, 1000);
    });
}

// 表单提交事件
$('#opportunityForm').on('submit', function(e) {
    e.preventDefault();
    addOpportunity();
});
</script>
{% endblock %}
