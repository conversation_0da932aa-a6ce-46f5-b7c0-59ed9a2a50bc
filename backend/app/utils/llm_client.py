"""
大模型客户端管理器
支持DeepSeek和Qwen-VL-Max模型的调用
"""

import os
import asyncio
from typing import Dict, Any, Optional, List
from openai import AsyncOpenAI
from dotenv import load_dotenv
import logging

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)


class LLMClient:
    """大模型客户端管理器"""
    
    def __init__(self):
        # DeepSeek配置（主要模型）
        self.deepseek_client = AsyncOpenAI(
            api_key=os.getenv("DEEPSEEK_API_KEY", "***********************************"),
            base_url=os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")
        )
        self.deepseek_model = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")
        
        # Qwen-VL-Max配置（备选模型）
        self.qwen_client = AsyncOpenAI(
            api_key=os.getenv("QWEN_API_KEY", "sk-8bf76568700746edb7e2a423b458854c"),
            base_url=os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        )
        self.qwen_model = os.getenv("QWEN_MODEL", "qwen-vl-max")
        
        # 当前使用的客户端
        self.current_client = self.deepseek_client
        self.current_model = self.deepseek_model
        self.current_provider = "deepseek"
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        temperature: float = 0.1,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式输出
            
        Returns:
            响应结果
        """
        try:
            # 首先尝试主要模型（DeepSeek）
            response = await self._try_completion(
                client=self.deepseek_client,
                model=self.deepseek_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            
            if response:
                self.current_provider = "deepseek"
                return response
                
        except Exception as e:
            logger.warning(f"DeepSeek API调用失败: {e}")
            
        try:
            # 备选模型（Qwen-VL-Max）
            logger.info("尝试使用备选模型 Qwen-VL-Max")
            response = await self._try_completion(
                client=self.qwen_client,
                model=self.qwen_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            
            if response:
                self.current_provider = "qwen"
                return response
                
        except Exception as e:
            logger.error(f"Qwen API调用失败: {e}")
            
        # 如果所有模型都失败，返回模拟响应
        logger.warning("所有模型调用失败，使用模拟响应")
        return self._get_mock_response(messages)
    
    async def _try_completion(
        self,
        client: AsyncOpenAI,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float,
        max_tokens: Optional[int],
        stream: bool
    ) -> Optional[Dict[str, Any]]:
        """尝试调用指定的模型"""
        try:
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            
            if stream:
                return {"stream": response}
            else:
                return {
                    "content": response.choices[0].message.content,
                    "usage": response.usage.dict() if response.usage else None,
                    "model": model
                }
                
        except Exception as e:
            logger.error(f"模型 {model} 调用失败: {e}")
            return None
    
    async def stream_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.1,
        max_tokens: Optional[int] = None
    ):
        """流式聊天完成"""
        try:
            # 首先尝试DeepSeek
            async for chunk in self._try_stream_completion(
                client=self.deepseek_client,
                model=self.deepseek_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            ):
                if chunk:
                    yield chunk
                    return
        except Exception as e:
            logger.warning(f"DeepSeek流式调用失败: {e}")
        
        try:
            # 备选Qwen
            logger.info("尝试使用备选模型 Qwen-VL-Max 进行流式调用")
            async for chunk in self._try_stream_completion(
                client=self.qwen_client,
                model=self.qwen_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            ):
                if chunk:
                    yield chunk
                    return
        except Exception as e:
            logger.error(f"Qwen流式调用失败: {e}")
        
        # 模拟流式响应
        mock_content = self._get_mock_response(messages)["content"]
        words = mock_content.split()
        for i, word in enumerate(words):
            yield {
                "content": word + " ",
                "finish_reason": "stop" if i == len(words) - 1 else None
            }
            await asyncio.sleep(0.1)
    
    async def _try_stream_completion(
        self,
        client: AsyncOpenAI,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float,
        max_tokens: Optional[int]
    ):
        """尝试流式调用指定模型"""
        try:
            stream = await client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield {
                        "content": chunk.choices[0].delta.content,
                        "finish_reason": chunk.choices[0].finish_reason
                    }
        except Exception as e:
            logger.error(f"模型 {model} 流式调用失败: {e}")
            yield None
    
    def _get_mock_response(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """生成模拟响应"""
        return {
            "content": """# 简历匹配度分析报告

## 基本信息
- **岗位**: 示例岗位
- **简历**: 示例简历
- **总体匹配度**: 75/100

## 详细分析

### 1. 技能匹配度 (18/25)
- **匹配的技能**: Python, JavaScript, SQL
- **缺失的关键技能**: React, Docker
- **分析说明**: 候选人具备大部分核心技能，但缺少部分前端和容器化技能

### 2. 工作经验匹配度 (20/25)
- **相关经验**: 3年软件开发经验
- **经验年限**: 符合要求
- **行业背景**: 互联网行业
- **分析说明**: 工作经验丰富，行业背景匹配

### 3. 教育背景匹配度 (15/25)
- **学历水平**: 本科
- **专业相关性**: 计算机科学
- **分析说明**: 教育背景良好，专业对口

### 4. 综合素质匹配度 (22/25)
- **软技能**: 团队协作、沟通能力强
- **发展潜力**: 学习能力强，有上进心
- **分析说明**: 综合素质优秀，发展潜力大

## 总结
### 优势
- 技术基础扎实
- 工作经验丰富
- 学习能力强

### 不足
- 缺少部分新技术经验
- 项目管理经验不足

### 建议
- 加强前端技术学习
- 提升项目管理能力

*注意: 当前使用模拟模式，请配置正确的API密钥以获得真实的AI分析结果。*
""",
            "usage": {"total_tokens": 500},
            "model": "mock"
        }
    
    def get_current_provider(self) -> str:
        """获取当前使用的模型提供商"""
        return self.current_provider
    
    async def test_connection(self) -> Dict[str, bool]:
        """测试所有模型的连接状态"""
        results = {}
        
        # 测试DeepSeek
        try:
            response = await self.deepseek_client.chat.completions.create(
                model=self.deepseek_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            results["deepseek"] = True
        except Exception as e:
            logger.error(f"DeepSeek连接测试失败: {e}")
            results["deepseek"] = False
        
        # 测试Qwen
        try:
            response = await self.qwen_client.chat.completions.create(
                model=self.qwen_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            results["qwen"] = True
        except Exception as e:
            logger.error(f"Qwen连接测试失败: {e}")
            results["qwen"] = False
        
        return results


# 全局LLM客户端实例
llm_client = LLMClient()
