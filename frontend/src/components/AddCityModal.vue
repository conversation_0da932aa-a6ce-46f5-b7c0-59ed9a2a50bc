<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative glass-card p-6 w-full max-w-md">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-white">添加/更新城市数据</h3>
        <button @click="$emit('close')" class="text-white/70 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            城市名称 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.cityName"
            type="text"
            class="input-gemini w-full"
            placeholder="请输入城市名称"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            个人缴纳金额 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.personalAmount"
            type="number"
            step="0.01"
            min="0"
            class="input-gemini w-full"
            placeholder="请输入个人缴纳金额"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            公司缴纳金额 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.companyAmount"
            type="number"
            step="0.01"
            min="0"
            class="input-gemini w-full"
            placeholder="请输入公司缴纳金额"
            required
          />
        </div>
        
        <div class="glass border border-white/10 rounded-lg p-3">
          <div class="text-sm text-white/70 mb-2">预览</div>
          <div class="text-sm text-white">
            <div>个人缴纳：¥{{ parseFloat(form.personalAmount || 0).toFixed(2) }}</div>
            <div>公司缴纳：¥{{ parseFloat(form.companyAmount || 0).toFixed(2) }}</div>
            <div class="font-semibold">总计：¥{{ (parseFloat(form.personalAmount || 0) + parseFloat(form.companyAmount || 0)).toFixed(2) }}</div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="glass-card px-4 py-2 text-white hover:bg-white/10 transition-all"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="loading || !canSubmit"
            class="btn-gemini px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="loading" class="animate-spin w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'

export default {
  name: 'AddCityModal',
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()
    
    const form = ref({
      cityName: '',
      personalAmount: '',
      companyAmount: ''
    })
    
    const loading = ref(false)
    
    const canSubmit = computed(() => {
      return form.value.cityName.trim() &&
             form.value.personalAmount !== '' &&
             form.value.companyAmount !== '' &&
             parseFloat(form.value.personalAmount) >= 0 &&
             parseFloat(form.value.companyAmount) >= 0
    })
    
    const handleSubmit = async () => {
      if (!canSubmit.value) {
        notificationStore.warning('请填写完整的城市信息')
        return
      }
      
      try {
        loading.value = true
        
        const result = await quotationStore.addCity({
          city_name: form.value.cityName.trim(),
          personal_amount: parseFloat(form.value.personalAmount),
          company_amount: parseFloat(form.value.companyAmount)
        })
        
        if (result.success) {
          emit('success')
          emit('close')
        } else {
          notificationStore.error(result.message || '添加失败')
        }
      } catch (error) {
        console.error('添加城市失败:', error)
        notificationStore.error('添加失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      loading,
      canSubmit,
      handleSubmit
    }
  }
}
</script>
