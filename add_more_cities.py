#!/usr/bin/env python3
"""
添加更多城市数据来测试城市显示
"""
import requests
import json

def add_more_cities():
    """添加更多城市数据"""
    base_url = "http://localhost:5001"
    
    print("🏙️ 添加更多城市数据测试城市显示")
    print("=" * 50)
    
    # 要添加的城市数据
    new_cities = [
        {
            "city_name": "成都",
            "social_base": 3800.0,
            "company_amount": 1064.0
        },
        {
            "city_name": "武汉",
            "social_base": 3600.0,
            "company_amount": 1008.0
        },
        {
            "city_name": "西安",
            "social_base": 3400.0,
            "company_amount": 952.0
        },
        {
            "city_name": "南京",
            "social_base": 4100.0,
            "company_amount": 1148.0
        },
        {
            "city_name": "天津",
            "social_base": 3900.0,
            "company_amount": 1092.0
        },
        {
            "city_name": "重庆",
            "social_base": 3500.0,
            "company_amount": 980.0
        },
        {
            "city_name": "苏州",
            "social_base": 4300.0,
            "company_amount": 1204.0
        },
        {
            "city_name": "青岛",
            "social_base": 3700.0,
            "company_amount": 1036.0
        }
    ]
    
    # 1. 检查当前城市数量
    print("\n1. 检查当前城市数量")
    try:
        response = requests.get(f"{base_url}/api/cities")
        if response.status_code == 200:
            current_cities = response.json()
            print(f"✅ 当前城市数量: {len(current_cities)}")
            
            existing_names = {city['city_name'] for city in current_cities}
            print(f"   现有城市: {', '.join(existing_names)}")
        else:
            print(f"❌ 获取城市失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 获取城市异常: {e}")
        return
    
    # 2. 添加新城市
    print(f"\n2. 添加新城市")
    added_count = 0
    
    for city_data in new_cities:
        if city_data['city_name'] not in existing_names:
            try:
                response = requests.post(
                    f"{base_url}/api/cities",
                    headers={'Content-Type': 'application/json'},
                    data=json.dumps(city_data)
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"✅ 添加城市: {city_data['city_name']}")
                        added_count += 1
                    else:
                        print(f"❌ 添加失败: {city_data['city_name']} - {result.get('message')}")
                else:
                    print(f"❌ 添加失败: {city_data['city_name']} - HTTP {response.status_code}")
            except Exception as e:
                print(f"❌ 添加异常: {city_data['city_name']} - {e}")
        else:
            print(f"⚠️ 城市已存在: {city_data['city_name']}")
    
    print(f"\n   成功添加 {added_count} 个新城市")
    
    # 3. 验证最终城市数量
    print(f"\n3. 验证最终城市数量")
    try:
        response = requests.get(f"{base_url}/api/cities")
        if response.status_code == 200:
            final_cities = response.json()
            print(f"✅ 最终城市数量: {len(final_cities)}")
            
            print(f"   所有城市:")
            for i, city in enumerate(final_cities, 1):
                print(f"   {i:2d}. {city['city_name']} (社保基数: ¥{city['social_base']})")
            
            # 测试城市显示
            if len(final_cities) > 8:
                print(f"\n✅ 城市数量充足 ({len(final_cities)} 个)，可以测试滚动显示")
                print(f"   - 页面应该显示滚动条")
                print(f"   - 响应式布局应该正常工作")
                print(f"   - 所有城市都应该可见")
            else:
                print(f"\n⚠️ 城市数量较少 ({len(final_cities)} 个)，可能不需要滚动")
                
        else:
            print(f"❌ 验证失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 验证异常: {e}")

def test_city_display_with_more_data():
    """测试更多城市数据的显示"""
    print(f"\n4. 测试城市显示效果")
    
    print(f"✅ 布局测试场景:")
    print(f"   1. 访问: http://localhost:3000/quotation")
    print(f"   2. 查看'选择社保城市'区域")
    print(f"   3. 验证所有城市都能显示")
    print(f"   4. 测试滚动功能（如果需要）")
    print(f"   5. 测试响应式布局（调整浏览器宽度）")
    
    print(f"\n✅ 交互测试场景:")
    print(f"   1. 选择多个城市")
    print(f"   2. 查看已选城市标签")
    print(f"   3. 测试单个城市移除")
    print(f"   4. 测试清空所有选择")
    print(f"   5. 验证选择状态反馈")
    
    print(f"\n✅ 视觉测试场景:")
    print(f"   1. 检查城市项目 hover 效果")
    print(f"   2. 验证滚动条样式")
    print(f"   3. 检查响应式布局切换")
    print(f"   4. 验证已选标签样式")
    print(f"   5. 检查整体视觉层次")

def main():
    """主函数"""
    print("🚀 开始添加更多城市数据...")
    
    add_more_cities()
    test_city_display_with_more_data()
    
    print("\n" + "=" * 50)
    print("✨ 城市数据添加完成！")
    
    print("\n📋 测试建议:")
    print("1. 🌐 访问报价生成页面")
    print("   http://localhost:3000/quotation")
    
    print("\n2. 🔍 检查城市显示")
    print("   - 所有城市都能看到")
    print("   - 布局整齐美观")
    print("   - 滚动功能正常")
    print("   - 响应式布局工作")
    
    print("\n3. 🖱️ 测试交互功能")
    print("   - 选择/取消选择城市")
    print("   - 查看已选城市标签")
    print("   - 使用移除按钮")
    print("   - 使用清空按钮")
    
    print("\n4. 📱 测试响应式")
    print("   - 调整浏览器宽度")
    print("   - 检查移动端显示")
    print("   - 验证布局适应性")
    
    print("\n💡 如果仍有显示问题:")
    print("- 检查浏览器控制台错误")
    print("- 尝试刷新页面")
    print("- 检查网络连接")
    print("- 验证数据加载状态")

if __name__ == '__main__':
    main()
