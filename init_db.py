#!/usr/bin/env python3
"""
初始化数据库脚本
"""
from app import app, db, Opportunity, CitySocialInsurance, PositionCost

def init_database():
    """初始化数据库"""
    with app.app_context():
        # 删除所有表
        db.drop_all()
        
        # 创建所有表
        db.create_all()
        
        print("数据库表创建成功！")
        
        # 添加示例数据
        add_sample_data()

def add_sample_data():
    """添加示例数据"""
    # 添加示例商机
    opportunities = [
        {'name': '某大型企业人力资源外包项目', 'description': '为某大型制造企业提供全国多地人力资源外包服务'},
        {'name': '互联网公司技术团队外包', 'description': '为互联网公司提供技术开发团队外包服务'},
        {'name': '金融机构客服中心项目', 'description': '为银行提供客服中心人员外包服务'},
    ]
    
    for opp_data in opportunities:
        opportunity = Opportunity(**opp_data)
        db.session.add(opportunity)
    
    # 添加示例城市数据
    cities = [
        {'city_name': '北京', 'social_base': 5000.00, 'company_amount': 2800.75},
        {'city_name': '上海', 'social_base': 4800.00, 'company_amount': 2650.80},
        {'city_name': '广州', 'social_base': 4200.00, 'company_amount': 2200.45},
        {'city_name': '深圳', 'social_base': 4600.00, 'company_amount': 2500.90},
        {'city_name': '杭州', 'social_base': 4000.00, 'company_amount': 2100.30},
    ]
    
    for city_data in cities:
        city = CitySocialInsurance(**city_data)
        db.session.add(city)
    
    # 添加示例岗位数据
    positions = [
        {'position_name': '软件开发工程师', 'monthly_salary': 15000.00, 'daily_salary': 682.00},
        {'position_name': '项目经理', 'monthly_salary': 20000.00, 'daily_salary': 909.00},
        {'position_name': '测试工程师', 'monthly_salary': 12000.00, 'daily_salary': 545.00},
        {'position_name': '产品经理', 'monthly_salary': 18000.00, 'daily_salary': 818.00},
        {'position_name': 'UI设计师', 'monthly_salary': 13000.00, 'daily_salary': 591.00},
        {'position_name': '运维工程师', 'monthly_salary': 14000.00, 'daily_salary': 636.00},
        {'position_name': '客服专员', 'monthly_salary': 8000.00, 'daily_salary': 364.00},
        {'position_name': '销售代表', 'monthly_salary': 10000.00, 'daily_salary': 455.00},
    ]
    
    for pos_data in positions:
        position = PositionCost(**pos_data)
        db.session.add(position)
    
    db.session.commit()
    print("示例数据添加成功！")

if __name__ == '__main__':
    init_database()
