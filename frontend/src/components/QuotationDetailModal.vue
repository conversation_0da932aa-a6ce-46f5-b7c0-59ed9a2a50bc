<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative glass-card p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-semibold text-white">报价详情</h3>
        <div class="flex items-center space-x-3">
          <button
            @click="$emit('export', record.record.id)"
            class="btn-gemini px-4 py-2"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            导出Excel
          </button>
          <button @click="$emit('close')" class="text-white/70 hover:text-white">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>
      
      <div v-if="record" class="space-y-6">
        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="glass border border-white/10 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-white mb-3">基本信息</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-white/70">报价ID：</span>
                <span class="text-white">{{ record.record.id }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-white/70">商机名称：</span>
                <span class="text-white">{{ record.record.opportunity_name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-white/70">计算类型：</span>
                <span class="text-white">{{ record.record.calculation_type === 'monthly' ? '按月计算' : '按天计算' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-white/70">生成时间：</span>
                <span class="text-white">{{ record.record.created_at }}</span>
              </div>
            </div>
          </div>
          
          <div class="glass border border-white/10 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-white mb-3">工期信息</h4>
            <div class="space-y-2 text-sm">
              <div v-if="record.record.calculation_type === 'monthly'" class="flex justify-between">
                <span class="text-white/70">工期：</span>
                <span class="text-white">{{ record.record.work_duration_months }}个月</span>
              </div>
              <div v-else>
                <div class="flex justify-between">
                  <span class="text-white/70">工期：</span>
                  <span class="text-white">{{ record.record.work_duration_days }}天</span>
                </div>
              </div>
              <div class="flex justify-between">
                <span class="text-white/70">报价系数：</span>
                <span class="text-white">{{ record.record.quotation_coefficient }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-white/70">选择城市：</span>
                <span class="text-white">{{ record.record.selected_cities.join(', ') }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-white/70">选择岗位：</span>
                <span class="text-white">{{ record.record.selected_positions.join(', ') }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 总金额 -->
        <div class="glass border border-green-500/30 rounded-lg p-6 text-center">
          <div class="text-lg text-white/70 mb-2">总报价金额</div>
          <div class="text-4xl font-bold gradient-text">¥{{ record.record.total_amount.toFixed(2) }}</div>
        </div>
        
        <!-- 明细表格 -->
        <div class="glass border border-white/10 rounded-lg p-4">
          <h4 class="text-lg font-semibold text-white mb-4">报价明细</h4>
          <div class="overflow-x-auto">
            <table class="w-full text-sm">
              <thead>
                <tr class="border-b border-white/20">
                  <th class="text-left py-3 px-2 text-white/70">城市</th>
                  <th class="text-left py-3 px-2 text-white/70">岗位</th>
                  <th class="text-left py-3 px-2 text-white/70">级别</th>
                  <th class="text-left py-3 px-2 text-white/70">岗位人数</th>
                  <th class="text-left py-3 px-2 text-white/70" v-if="record.record.calculation_type === 'monthly'">月工资</th>
                  <th class="text-left py-3 px-2 text-white/70" v-else>日工资</th>
                  <th class="text-left py-3 px-2 text-white/70">公司社保</th>
                  <th class="text-left py-3 px-2 text-white/70">成本</th>
                  <th class="text-left py-3 px-2 text-white/70">报价金额</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="(cityGroup, cityName) in groupedDetails" :key="cityName">
                  <!-- 城市明细行 -->
                  <tr v-for="(detail, index) in cityGroup.details" :key="`${cityName}-${index}`"
                      class="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td class="py-3 px-2 text-white">{{ detail.city_name }}</td>
                    <td class="py-3 px-2 text-white">{{ detail.position_name }}</td>
                    <td class="py-3 px-2 text-white">{{ detail.position_level || '—' }}</td>
                    <td class="py-3 px-2 text-white text-center">{{ detail.count || 1 }}</td>
                    <td class="py-3 px-2 text-white" v-if="record.record.calculation_type === 'monthly'">
                      ¥{{ detail.monthly_salary?.toFixed(2) || '0.00' }}
                    </td>
                    <td class="py-3 px-2 text-white" v-else>
                      ¥{{ detail.daily_salary?.toFixed(2) || '0.00' }}
                    </td>
                    <td class="py-3 px-2 text-white text-center">
                      <span :title="`单月/单次：¥${detail.company_insurance?.toFixed(2) || '0.00'}`">
                        <template v-if="record.record.calculation_type === 'monthly'">
                          ¥{{ (detail.company_insurance * (detail.work_duration_months || 1)).toFixed(2) }}
                        </template>
                        <template v-else>
                          ¥{{ (detail.company_insurance * Math.round(detail.work_duration_days / 30)).toFixed(2) }}
                        </template>
                      </span>
                    </td>
                    <td class="py-3 px-2 text-white font-semibold">¥{{ detail.subtotal?.toFixed(2) || '0.00' }}</td>
                    <td class="py-3 px-2 text-white font-semibold">
                      ¥{{ (detail.external_price || detail.subtotal || 0).toFixed(2) }}
                    </td>
                  </tr>
                  <!-- 城市汇总行 -->
                  <tr class="bg-blue-500/10 border-b-2 border-blue-500/30">
                    <td colspan="9" class="py-2 px-2 text-blue-300 font-semibold text-left">
                      {{ cityName }} 合计：
                      <span class="ml-6">成本合计 <span class="font-bold">¥{{ cityGroup.total.toFixed(2) }}</span></span>
                      <span class="ml-6">报价合计 <span class="font-bold">¥{{ (cityGroup.quoteTotal || cityGroup.total).toFixed(2) }}</span></span>
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- 计算说明 -->
        <div class="glass border border-blue-500/30 rounded-lg p-4">
          <h4 class="text-lg font-semibold text-white mb-3">计算说明</h4>
          <div class="text-sm text-white/70 space-y-1">
            <div v-if="record.record.calculation_type === 'monthly'">
              <strong>按月计算公式：</strong>
              <div class="ml-4">月度总成本 = 月工资 + 个人社保公积金 + 公司社保公积金</div>
              <div class="ml-4">小计 = 月度总成本 × 工期（月）</div>
            </div>
            <div v-else>
              <strong>按天计算公式：</strong>
              <div class="ml-4">月度总成本 = 日工资 × 考勤基数 + 公司社保公积金</div>
              <div class="ml-4">小计 = 月度总成本 × 工期（天）</div>
            </div>
            <div><strong>总报价 = 所有组合小计之和</strong></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'QuotationDetailModal',
  props: {
    record: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'export'],
  setup(props) {
    // 按城市分组的报价明细
    const groupedDetails = computed(() => {
      if (!props.record || !props.record.details) {
        return {}
      }

      const groups = {}
      props.record.details.forEach(detail => {
        const cityName = detail.city_name
        if (!groups[cityName]) {
          groups[cityName] = {
            details: [],
            total: 0,
            quoteTotal: 0
          }
        }
        groups[cityName].details.push(detail)
        groups[cityName].total += detail.subtotal
        groups[cityName].quoteTotal += (detail.external_price || detail.subtotal || 0)
      })

      return groups
    })

    return {
      groupedDetails
    }
  }
}
</script>

<style scoped>
/* 表格样式 */
tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4285f4, #9c27b0);
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9c27b0, #e91e63);
}
</style>
