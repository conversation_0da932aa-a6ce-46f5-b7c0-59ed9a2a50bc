import React from 'react';
import { Avatar, Typography, Space, Card } from 'antd';
import { UserOutlined, RobotOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import './MessageList.css';

const { Text } = Typography;

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  streaming?: boolean;
}

interface MessageListProps {
  messages: Message[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessage = (message: Message) => {
    const isUser = message.role === 'user';
    
    return (
      <div key={message.id} className={`message-wrapper ${isUser ? 'user-message' : 'assistant-message'}`}>
        <div className="message-content">
          <div className="message-avatar">
            <Avatar 
              size={36}
              icon={isUser ? <UserOutlined /> : <RobotOutlined />}
              className={isUser ? 'user-avatar' : 'assistant-avatar'}
            />
          </div>
          
          <div className="message-body">
            <div className="message-header">
              <Text className="message-sender">
                {isUser ? 'You' : 'AutoGen Assistant'}
              </Text>
              <Text className="message-time">
                {formatTime(message.timestamp)}
              </Text>
            </div>
            
            <Card className={`message-card ${isUser ? 'user-card' : 'assistant-card'}`}>
              <div className="message-text">
                {isUser ? (
                  <Text>{message.content}</Text>
                ) : (
                  <div className="markdown-content">
                    <ReactMarkdown
                      components={{
                        code({ node, inline, className, children, ...props }) {
                          const match = /language-(\w+)/.exec(className || '');
                          return !inline && match ? (
                            <SyntaxHighlighter
                              style={vscDarkPlus}
                              language={match[1]}
                              PreTag="div"
                              className="code-block"
                              {...props}
                            >
                              {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                          ) : (
                            <code className="inline-code" {...props}>
                              {children}
                            </code>
                          );
                        },
                        p: ({ children }) => <p className="markdown-paragraph">{children}</p>,
                        ul: ({ children }) => <ul className="markdown-list">{children}</ul>,
                        ol: ({ children }) => <ol className="markdown-list">{children}</ol>,
                        li: ({ children }) => <li className="markdown-list-item">{children}</li>,
                        h1: ({ children }) => <h1 className="markdown-heading">{children}</h1>,
                        h2: ({ children }) => <h2 className="markdown-heading">{children}</h2>,
                        h3: ({ children }) => <h3 className="markdown-heading">{children}</h3>,
                        blockquote: ({ children }) => <blockquote className="markdown-blockquote">{children}</blockquote>,
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                    {message.streaming && (
                      <span className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </span>
                    )}
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="message-list">
      {messages.map(renderMessage)}
    </div>
  );
};

export default MessageList;
