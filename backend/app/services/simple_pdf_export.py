"""
简化的PDF导出服务（避免中文编码问题）
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
import io
import re
from typing import List
from ..models.database import MatchResult


class SimplePDFExportService:
    """简化的PDF导出服务类"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
    
    def export_single_result(self, result: MatchResult) -> bytes:
        """导出单个匹配结果为PDF"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # 构建内容
        story = []
        
        # 标题
        story.append(Paragraph("Resume Matching Analysis Report", self.styles['Title']))
        story.append(Spacer(1, 20))
        
        # 基本信息表格（避免中文字符）
        candidate_name = self._safe_text(result.resume.candidate_name or self._extract_name_from_filename(result.resume.original_filename))

        basic_info = [
            ['Project', self._safe_text(result.job.project or 'Unspecified')],
            ['Position', self._safe_text(result.job.title or 'Unknown')],
            ['Candidate', candidate_name],
            ['Resume File', self._safe_text(result.resume.original_filename or 'Unknown')],
            ['Match Score', f"{result.match_score:.1f}/100"],
            ['Analysis Date', result.created_at.strftime("%Y-%m-%d")]
        ]
        
        basic_table = Table(basic_info, colWidths=[2*inch, 4*inch])
        basic_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        story.append(basic_table)
        story.append(Spacer(1, 20))
        
        # 三维度评分表格
        dimensions = self._extract_dimension_scores(result.analysis_result or "")
        if dimensions:
            story.append(Paragraph("Three-Dimension Scoring Details", self.styles['Heading2']))
            
            score_data = [
                ['Dimension', 'Score', 'Max Score'],
                ['Skills Match', f"{dimensions.get('skill', 0)}", '50'],
                ['Experience Match', f"{dimensions.get('experience', 0)}", '30'],
                ['Education Match', f"{dimensions.get('education', 0)}", '20']
            ]
            
            score_table = Table(score_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
            score_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            story.append(score_table)
            story.append(Spacer(1, 20))
        
        # 简化的分析内容
        if result.analysis_result:
            story.append(Paragraph("Analysis Summary", self.styles['Heading2']))
            
            # 提取关键信息
            summary = self._extract_analysis_summary(result.analysis_result)
            if summary:
                story.append(Paragraph(summary, self.styles['Normal']))
            else:
                story.append(Paragraph("Analysis completed successfully.", self.styles['Normal']))
        
        # 生成PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def export_multiple_results(self, results: List[MatchResult]) -> bytes:
        """导出多个匹配结果为PDF"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        story = []
        
        # 标题
        story.append(Paragraph("Batch Matching Results Report", self.styles['Title']))
        story.append(Spacer(1, 20))
        
        # 汇总表格
        summary_data = [['No.', 'Candidate', 'Position', 'Project', 'Score', 'Date']]
        
        for i, result in enumerate(results, 1):
            candidate_name = result.resume.candidate_name or self._extract_name_from_filename(result.resume.original_filename)
            summary_data.append([
                str(i),
                candidate_name[:20],  # 限制长度
                result.job.title[:30] if result.job.title else 'Unknown',
                result.job.project[:20] if result.job.project else 'Unspecified',
                f"{result.match_score:.1f}",
                result.created_at.strftime("%Y-%m-%d")
            ])
        
        summary_table = Table(summary_data, colWidths=[0.5*inch, 1.5*inch, 2*inch, 1.5*inch, 1*inch, 1*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        story.append(summary_table)
        
        # 生成PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def _extract_name_from_filename(self, filename: str) -> str:
        """从文件名中提取候选人姓名"""
        if not filename:
            return "Unknown"
        
        name_part = filename.rsplit('.', 1)[0] if '.' in filename else filename
        separators = ['_', '-', 'resume', 'cv']
        
        for sep in separators:
            if sep in name_part.lower():
                parts = name_part.split(sep)
                if parts[0].strip():
                    return parts[0].strip()[:20]  # 限制长度
        
        return name_part[:20] if len(name_part) > 20 else name_part
    
    def _extract_dimension_scores(self, text: str) -> dict:
        """提取三维度评分"""
        dimensions = {}
        
        if not text:
            return dimensions
        
        # 提取技能匹配度
        skill_patterns = [
            r'技能匹配度[^(]*\((\d+)/50\)',
            r'技能匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/50',
            r'\*\*评分\*\*:\s*(\d+)/50'
        ]
        
        for pattern in skill_patterns:
            match = re.search(pattern, text)
            if match:
                dimensions['skill'] = int(match.group(1))
                break
        
        # 提取工作经验匹配度
        exp_patterns = [
            r'工作经验匹配度[^(]*\((\d+)/30\)',
            r'工作经验匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/30'
        ]
        
        for pattern in exp_patterns:
            match = re.search(pattern, text)
            if match:
                dimensions['experience'] = int(match.group(1))
                break
        
        # 提取教育背景匹配度
        edu_patterns = [
            r'教育背景匹配度[^(]*\((\d+)/20\)',
            r'教育背景匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/20'
        ]
        
        for pattern in edu_patterns:
            match = re.search(pattern, text)
            if match:
                dimensions['education'] = int(match.group(1))
                break
        
        return dimensions
    
    def _extract_analysis_summary(self, analysis_text: str) -> str:
        """提取分析摘要"""
        if not analysis_text:
            return ""
        
        # 提取前几行作为摘要
        lines = analysis_text.split('\n')
        summary_lines = []
        
        for line in lines[:10]:  # 只取前10行
            line = line.strip()
            if line and not line.startswith('#'):
                # 移除Markdown格式
                clean_line = re.sub(r'\*\*(.*?)\*\*', r'\1', line)
                clean_line = re.sub(r'[#*`]', '', clean_line)
                if clean_line:
                    summary_lines.append(clean_line)
        
        return ' '.join(summary_lines[:3])  # 只取前3个有效行
