# AI驱动的商机报价系统

这是一个基于Flask开发的智能商机报价系统，能够根据用户输入的商机信息、地区和岗位选择，自动生成详细的报价明细表，并具备完整的报价记录管理功能。

## 功能特点

### 🎯 核心功能
- **智能报价生成**：根据商机、地区、岗位自动计算报价明细
- **多维度选择**：支持多地区、多岗位组合报价
- **成本计算**：自动计算工资、社保、公积金等各项成本
- **工期灵活**：支持自定义工期设置

### 📊 数据管理
- **商机管理**：商机信息的录入和维护
- **城市数据**：各城市社保公积金数据管理
- **岗位成本**：不同岗位工资成本维护
- **Excel导入**：支持批量导入城市数据

### 📈 报表功能
- **报价记录**：完整的历史报价记录查看
- **详细明细**：每个报价的详细明细展示
- **Excel导出**：支持单个和批量报价导出
- **数据统计**：报价数据的统计分析

## 技术架构

- **后端框架**：Flask + SQLAlchemy
- **数据库**：SQLite
- **前端技术**：Bootstrap 5 + jQuery
- **Excel处理**：pandas + openpyxl
- **UI组件**：Bootstrap Icons

## 安装和运行

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd price

# 安装依赖
pip install -r requirements.txt
```

### 2. 初始化数据
```bash
# 创建示例数据（可选）
python create_sample_data.py
```

### 3. 启动应用
```bash
python app.py
```

访问 http://localhost:5000 即可使用系统。

## 使用指南

### 数据准备

#### 1. 商机管理
- 进入"数据管理" -> "商机管理"
- 点击"添加商机"录入商机信息
- 商机名称将在报价时使用

#### 2. 城市社保公积金数据
- 进入"数据管理" -> "城市社保公积金"
- 可以手动添加或通过Excel批量导入
- Excel格式要求：
  - 城市名称
  - 个人缴纳金额
  - 公司缴纳金额

#### 3. 岗位成本数据
- 进入"数据管理" -> "岗位成本"
- 录入不同岗位的月工资标准

### 生成报价

#### 1. 设置报价参数
- 进入"生成报价"页面
- 输入商机名称（支持自动补全）
- 设置工期（月）
- 选择目标城市（可多选）
- 选择需要的岗位（可多选）

#### 2. 生成和查看
- 点击"生成报价"按钮
- 系统自动计算各项成本
- 显示详细的报价明细表
- 支持导出为Excel文件

### 报价管理

#### 1. 查看记录
- 进入"报价记录"页面
- 查看所有历史报价记录
- 支持查看详细明细

#### 2. 导出功能
- 单个报价导出：点击对应记录的"导出"按钮
- 批量导出：点击"导出所有记录"按钮
- Excel文件包含汇总信息和详细明细

## 数据库结构

### 主要数据表

1. **opportunities**（商机表）
   - id：主键
   - name：商机名称
   - description：描述
   - created_at：创建时间

2. **city_social_insurance**（城市社保公积金表）
   - id：主键
   - city_name：城市名称
   - personal_amount：个人缴纳金额
   - company_amount：公司缴纳金额

3. **position_costs**（岗位成本表）
   - id：主键
   - position_name：岗位名称
   - monthly_salary：月工资

4. **quotation_records**（报价记录表）
   - id：主键
   - opportunity_name：商机名称
   - selected_cities：选择的城市（JSON）
   - selected_positions：选择的岗位（JSON）
   - work_duration_months：工期
   - total_amount：总金额

5. **quotation_details**（报价明细表）
   - id：主键
   - quotation_id：报价记录ID
   - city_name：城市名称
   - position_name：岗位名称
   - monthly_salary：月工资
   - personal_insurance：个人社保公积金
   - company_insurance：公司社保公积金
   - subtotal：小计金额

## 计算逻辑

### 报价计算公式
```
月度总成本 = 月工资 + 个人社保公积金 + 公司社保公积金
工期小计 = 月度总成本 × 工期（月）
总报价 = 所有组合的工期小计之和
```

### 组合计算
系统会为每个"城市-岗位"组合生成一条明细记录，最终汇总所有明细得出总报价。

## 扩展功能

系统设计具有良好的扩展性，可以轻松添加以下功能：

- 报价审批流程
- 更复杂的成本计算模型
- 报价模板管理
- 客户管理
- 合同管理
- 数据分析和图表展示

## 注意事项

1. **数据备份**：定期备份SQLite数据库文件
2. **Excel格式**：导入Excel时请确保列名完全匹配
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari等）
4. **数据精度**：金额计算保留两位小数

## 技术支持

如有问题或建议，请联系开发团队。
