"""
匹配结果服务
"""

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc
from typing import List, Optional, Dict, Any
from ..models.database import MatchResult, Job, Resume
from docx import Document
from docx.shared import Inches
import io
import re
from .pdf_export_service import PDFExportService


class MatchResultService:
    """匹配结果服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_match_results(
        self,
        project: Optional[str] = None,
        job_id: Optional[int] = None,
        candidate_name: Optional[str] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """获取匹配结果列表"""
        
        # 构建查询
        query = self.db.query(MatchResult).options(
            joinedload(MatchResult.job),
            joinedload(MatchResult.resume)
        )
        
        # 添加过滤条件
        filters = []
        
        if project:
            filters.append(Job.project.ilike(f"%{project}%"))
        
        if job_id:
            filters.append(MatchResult.job_id == job_id)
        
        if candidate_name:
            filters.append(
                or_(
                    Resume.candidate_name.ilike(f"%{candidate_name}%"),
                    Resume.original_filename.ilike(f"%{candidate_name}%")
                )
            )
        
        if filters:
            query = query.join(Job).join(Resume).filter(and_(*filters))
        else:
            query = query.join(Job).join(Resume)
        
        # 按创建时间倒序排列
        query = query.order_by(desc(MatchResult.created_at))
        
        # 计算总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * size
        results = query.offset(offset).limit(size).all()
        
        # 格式化结果
        formatted_results = []
        for result in results:
            formatted_results.append({
                "id": result.id,
                "project": result.job.project,
                "job_title": result.job.title,
                "job_id": result.job_id,
                "candidate_name": result.resume.candidate_name or self._extract_name_from_filename(result.resume.original_filename),
                "resume_title": result.resume.original_filename,
                "resume_id": result.resume_id,
                "match_score": result.match_score,
                "created_at": result.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "analysis_date": result.created_at.strftime("%Y-%m-%d")
            })
        
        return {
            "results": formatted_results,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
    
    def get_match_result_by_id(self, result_id: int) -> Optional[MatchResult]:
        """根据ID获取匹配结果详情"""
        return self.db.query(MatchResult).options(
            joinedload(MatchResult.job),
            joinedload(MatchResult.resume)
        ).filter(MatchResult.id == result_id).first()
    
    def get_all_candidates(self) -> List[str]:
        """获取所有候选人列表"""
        # 从候选人姓名字段获取
        candidates_from_name = self.db.query(Resume.candidate_name).filter(
            Resume.candidate_name.isnot(None),
            Resume.candidate_name != ""
        ).distinct().all()
        
        candidates = [name[0] for name in candidates_from_name if name[0]]
        
        # 如果候选人姓名为空，从文件名提取
        resumes_without_name = self.db.query(Resume.original_filename).filter(
            or_(Resume.candidate_name.is_(None), Resume.candidate_name == "")
        ).all()
        
        for filename_tuple in resumes_without_name:
            filename = filename_tuple[0]
            extracted_name = self._extract_name_from_filename(filename)
            if extracted_name and extracted_name not in candidates:
                candidates.append(extracted_name)
        
        return sorted(candidates)
    
    def delete_match_result(self, result_id: int) -> bool:
        """删除匹配结果"""
        result = self.db.query(MatchResult).filter(MatchResult.id == result_id).first()
        if result:
            self.db.delete(result)
            self.db.commit()
            return True
        return False
    
    def export_single_result_to_word(self, result: MatchResult) -> bytes:
        """导出单个匹配结果为Word文档"""
        doc = Document()
        
        # 添加标题
        title = doc.add_heading('简历匹配度分析报告', 0)
        title.alignment = 1  # 居中对齐
        
        # 添加基本信息
        doc.add_heading('基本信息', level=1)
        
        info_table = doc.add_table(rows=6, cols=2)
        info_table.style = 'Table Grid'
        
        info_data = [
            ('项目', result.job.project),
            ('岗位', result.job.title),
            ('候选人', result.resume.candidate_name or self._extract_name_from_filename(result.resume.original_filename)),
            ('简历文件', result.resume.original_filename),
            ('匹配度评分', f"{result.match_score:.1f}/100"),
            ('分析日期', result.created_at.strftime("%Y年%m月%d日"))
        ]
        
        for i, (label, value) in enumerate(info_data):
            info_table.cell(i, 0).text = label
            info_table.cell(i, 1).text = str(value)
        
        # 添加详细分析
        if result.analysis_result:
            self._add_detailed_analysis_to_doc(doc, result.analysis_result)
        
        # 保存到字节流
        doc_io = io.BytesIO()
        doc.save(doc_io)
        doc_io.seek(0)
        
        return doc_io.getvalue()

    def export_single_result_to_pdf(self, result: MatchResult) -> bytes:
        """导出单个匹配结果为PDF"""
        pdf_service = PDFExportService()
        return pdf_service.export_single_result(result)

    def _extract_name_from_filename(self, filename: str) -> str:
        """从文件名中提取候选人姓名"""
        # 移除文件扩展名
        name_part = filename.rsplit('.', 1)[0] if '.' in filename else filename
        
        # 常见的分隔符
        separators = ['_', '-', '的简历', '简历', '_resume', '_cv']
        
        for sep in separators:
            if sep in name_part:
                parts = name_part.split(sep)
                if parts[0].strip():
                    return parts[0].strip()
        
        # 如果没有分隔符，尝试提取中文姓名（2-4个字符）
        chinese_name = re.search(r'^[\u4e00-\u9fa5]{2,4}', name_part)
        if chinese_name:
            return chinese_name.group()
        
        # 如果没有找到，返回文件名的前10个字符
        return name_part[:10] if len(name_part) > 10 else name_part
    
    def _add_detailed_analysis_to_doc(self, doc: Document, analysis_text: str):
        """将详细分析结果添加到Word文档"""
        # 分割分析结果和评审意见
        parts = analysis_text.split('---')
        analysis_part = parts[0] if parts else analysis_text
        review_part = parts[1] if len(parts) > 1 else ""
        
        # 添加分析结果部分
        doc.add_heading('匹配度分析', level=1)
        self._format_analysis_section(doc, analysis_part)
        
        # 如果有评审意见，添加评审部分
        if review_part.strip():
            doc.add_heading('专家评审意见', level=1)
            self._format_review_section(doc, review_part)
    
    def _format_analysis_section(self, doc: Document, analysis_text: str):
        """格式化分析部分"""
        # 提取三维度评分
        dimensions = self._extract_dimension_scores(analysis_text)
        
        if dimensions:
            # 创建评分表格
            score_table = doc.add_table(rows=4, cols=2)
            score_table.style = 'Table Grid'
            
            score_table.cell(0, 0).text = '评分维度'
            score_table.cell(0, 1).text = '得分'
            
            score_table.cell(1, 0).text = '技能匹配度'
            score_table.cell(1, 1).text = f"{dimensions.get('skill', 0)}/50"
            
            score_table.cell(2, 0).text = '工作经验匹配度'
            score_table.cell(2, 1).text = f"{dimensions.get('experience', 0)}/30"
            
            score_table.cell(3, 0).text = '教育背景匹配度'
            score_table.cell(3, 1).text = f"{dimensions.get('education', 0)}/20"
        
        # 格式化详细分析内容
        self._format_markdown_content(doc, analysis_text)
    
    def _format_review_section(self, doc: Document, review_text: str):
        """格式化评审部分"""
        self._format_markdown_content(doc, review_text)
    
    def _extract_dimension_scores(self, text: str) -> Dict[str, int]:
        """提取三维度评分"""
        dimensions = {}
        
        # 提取技能匹配度 - 支持新格式
        skill_patterns = [
            r'技能匹配度[^(]*\((\d+)/50\)',
            r'技能匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/50',
            r'\*\*评分\*\*:\s*(\d+)/50'
        ]
        
        for pattern in skill_patterns:
            skill_match = re.search(pattern, text)
            if skill_match:
                dimensions['skill'] = int(skill_match.group(1))
                break
        
        # 提取工作经验匹配度
        exp_patterns = [
            r'工作经验匹配度[^(]*\((\d+)/30\)',
            r'工作经验匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/30'
        ]
        
        for pattern in exp_patterns:
            exp_match = re.search(pattern, text)
            if exp_match:
                dimensions['experience'] = int(exp_match.group(1))
                break
        
        # 提取教育背景匹配度
        edu_patterns = [
            r'教育背景匹配度[^(]*\((\d+)/20\)',
            r'教育背景匹配度评估[^-]*-\s*\*\*评分\*\*:\s*(\d+)/20'
        ]
        
        for pattern in edu_patterns:
            edu_match = re.search(pattern, text)
            if edu_match:
                dimensions['education'] = int(edu_match.group(1))
                break
        
        return dimensions
    
    def _format_markdown_content(self, doc: Document, content: str):
        """格式化Markdown内容到Word文档"""
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 处理标题
            if line.startswith('###'):
                doc.add_heading(line[3:].strip(), level=3)
            elif line.startswith('##'):
                doc.add_heading(line[2:].strip(), level=2)
            elif line.startswith('#'):
                doc.add_heading(line[1:].strip(), level=1)
            
            # 处理列表
            elif line.startswith('- **') or line.startswith('* **'):
                # 粗体列表项
                para = doc.add_paragraph()
                para.style = 'List Bullet'
                bold_match = re.match(r'[-*]\s*\*\*(.*?)\*\*:\s*(.*)', line)
                if bold_match:
                    para.add_run(bold_match.group(1) + ': ').bold = True
                    para.add_run(bold_match.group(2))
                else:
                    para.add_run(line[2:])
            
            elif line.startswith('- ') or line.startswith('* '):
                # 普通列表项
                para = doc.add_paragraph(line[2:])
                para.style = 'List Bullet'
            
            # 处理普通段落
            else:
                # 处理粗体
                para = doc.add_paragraph()
                self._add_formatted_text(para, line)
    
    def _add_formatted_text(self, paragraph, text: str):
        """添加格式化文本到段落"""
        # 处理粗体 **text**
        parts = re.split(r'(\*\*.*?\*\*)', text)
        for part in parts:
            if part.startswith('**') and part.endswith('**'):
                paragraph.add_run(part[2:-2]).bold = True
            else:
                paragraph.add_run(part)
