{% extends "base.html" %}

{% block title %}岗位成本管理 - 商机报价系统{% endblock %}

{% block content %}
<div id="alerts"></div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">岗位成本管理</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPositionModal">
                    <i class="bi bi-plus"></i> 添加岗位
                </button>
            </div>
            <div class="card-body">
                {% if positions %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>岗位名称</th>
                                <th>月工资</th>
                                <th>更新时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for position in positions %}
                            <tr>
                                <td>{{ position.position_name }}</td>
                                <td>¥{{ "%.2f"|format(position.monthly_salary) }}</td>
                                <td>{{ position.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-person-badge display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无岗位数据</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPositionModal">
                        添加第一个岗位
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 添加岗位模态框 -->
<div class="modal fade" id="addPositionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加/更新岗位</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="positionForm">
                    <div class="mb-3">
                        <label for="positionName" class="form-label">岗位名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="positionName" required>
                    </div>
                    <div class="mb-3">
                        <label for="monthlySalary" class="form-label">月工资 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="monthlySalary" step="0.01" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addPosition()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addPosition() {
    const positionName = $('#positionName').val().trim();
    const monthlySalary = $('#monthlySalary').val();
    
    if (!positionName || !monthlySalary) {
        showAlert('请填写所有必填字段', 'danger');
        return;
    }
    
    const data = {
        position_name: positionName,
        monthly_salary: parseFloat(monthlySalary)
    };
    
    submitForm('/api/positions', data, function(response) {
        $('#addPositionModal').modal('hide');
        $('#positionForm')[0].reset();
        setTimeout(() => {
            location.reload();
        }, 1000);
    });
}

// 表单提交事件
$('#positionForm').on('submit', function(e) {
    e.preventDefault();
    addPosition();
});
</script>
{% endblock %}
