<template>
  <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="handleClose"></div>
    
    <!-- 模态框 -->
    <div class="relative w-full max-w-md mx-4">
      <form @submit.prevent="handleSubmit" class="glass-card p-6 rounded-xl border border-white/10 flex flex-col" style="max-height: 90vh;">
        <div class="flex-1 overflow-y-auto" style="max-height: 60vh;">
          <!-- 标题 -->
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-white">编辑城市数据</h3>
            <button @click="handleClose" class="text-white/50 hover:text-white transition-colors">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          <!-- 表单 -->
          <div>
            <label class="block text-sm font-medium text-white mb-2">
              城市名称 <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.cityName"
              type="text"
              class="input-gemini w-full"
              placeholder="请输入城市名称"
              required
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-white mb-2">
              社保基数 <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.socialBase"
              type="number"
              step="0.01"
              min="0"
              class="input-gemini w-full"
              placeholder="请输入社保基数"
              required
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-white mb-2">
              社保缴纳比例 (%) <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.socialRate"
              type="number"
              step="0.01"
              min="0"
              max="100"
              class="input-gemini w-full"
              placeholder="请输入社保缴纳比例，如16"
              required
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-white mb-2">
              公积金基数 <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.housingBase"
              type="number"
              step="0.01"
              min="0"
              class="input-gemini w-full"
              placeholder="请输入公积金基数"
              required
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-white mb-2">
              公积金缴纳比例 (%) <span class="text-red-400">*</span>
            </label>
            <input
              v-model="form.housingRate"
              type="number"
              step="0.01"
              min="0"
              max="100"
              class="input-gemini w-full"
              placeholder="请输入公积金缴纳比例，如12"
              required
            />
          </div>

          <!-- 预览 -->
          <div class="glass border border-white/10 rounded-lg p-3">
            <div class="text-sm text-white/70 mb-2">预览</div>
            <div class="text-sm text-white">
              <div>社保基数：¥{{ parseFloat(form.socialBase || 0).toFixed(2) }}</div>
              <div>社保缴纳比例：{{ parseFloat(form.socialRate || 0).toFixed(2) }}%</div>
              <div>公积金基数：¥{{ parseFloat(form.housingBase || 0).toFixed(2) }}</div>
              <div>公积金缴纳比例：{{ parseFloat(form.housingRate || 0).toFixed(2) }}%</div>
              <div class="text-green-400 font-semibold mt-2">
                公司缴纳总额：¥{{ calculateCompanyAmount().toFixed(2) }}
              </div>
              <div class="text-xs text-white/50 mt-1">注：公司缴纳总额 = 社保基数×社保比例 + 公积金基数×公积金比例</div>
            </div>
          </div>
        </div>
        <div class="flex space-x-3 pt-4 sticky bottom-0 bg-transparent z-10">
          <button type="button" @click="handleClose" class="flex-1 glass-card px-4 py-2 text-white hover:bg-white/10 transition-all">取消</button>
          <button type="submit" :disabled="!canSubmit || loading" class="flex-1 btn-gemini disabled:opacity-50 disabled:cursor-not-allowed">
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              更新中...
            </span>
            <span v-else>更新城市</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuotationStore } from '../stores/quotation.js'

const props = defineProps({
  visible: Boolean,
  city: Object
})

const emit = defineEmits(['close', 'success'])

const quotationStore = useQuotationStore()
const loading = ref(false)

const form = ref({
  cityName: '',
  socialBase: '',
  socialRate: '16',
  housingBase: '',
  housingRate: '12'
})

// 监听城市数据变化，填充表单
watch(() => props.city, (newCity) => {
  if (newCity) {
    form.value = {
      cityName: newCity.city_name,
      socialBase: newCity.social_base.toString(),
      socialRate: (newCity.social_rate * 100).toString(),
      housingBase: newCity.housing_base.toString(),
      housingRate: (newCity.housing_rate * 100).toString()
    }
  }
}, { immediate: true })

const canSubmit = computed(() => {
  return form.value.cityName.trim() &&
         form.value.socialBase !== '' &&
         form.value.socialRate !== '' &&
         form.value.housingBase !== '' &&
         form.value.housingRate !== '' &&
         parseFloat(form.value.socialBase) >= 0 &&
         parseFloat(form.value.socialRate) >= 0 &&
         parseFloat(form.value.housingBase) >= 0 &&
         parseFloat(form.value.housingRate) >= 0
})

const calculateCompanyAmount = () => {
  const socialBase = parseFloat(form.value.socialBase || 0)
  const socialRate = parseFloat(form.value.socialRate || 0) / 100
  const housingBase = parseFloat(form.value.housingBase || 0)
  const housingRate = parseFloat(form.value.housingRate || 0) / 100
  return socialBase * socialRate + housingBase * housingRate
}

const handleSubmit = async () => {
  if (!props.city) return
  
  try {
    loading.value = true
    const result = await quotationStore.updateCity(props.city.id, {
      city_name: form.value.cityName.trim(),
      social_base: parseFloat(form.value.socialBase),
      social_rate: parseFloat(form.value.socialRate) / 100,
      housing_base: parseFloat(form.value.housingBase),
      housing_rate: parseFloat(form.value.housingRate) / 100
    })
    
    if (result.success) {
      emit('success', result.message)
      handleClose()
    }
  } catch (error) {
    console.error('更新城市失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('close')
}
</script> 