<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative w-full max-w-md mx-4">
      <form @submit.prevent="handleSubmit" class="glass-card p-6 rounded-xl border border-white/10 flex flex-col" style="max-height: 90vh;">
        <div class="flex-1 overflow-y-auto" style="max-height: 60vh;">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-white">添加/更新岗位</h3>
            <button @click="$emit('close')" class="text-white/70 hover:text-white">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
          
          <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            岗位名称 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.positionName"
            type="text"
            class="input-gemini w-full"
            placeholder="请输入岗位名称"
            required
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-white mb-2">
            岗位级别 <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.positionLevel"
            class="select-gemini w-full"
            required
          >
            <option value="">请选择级别</option>
            <option value="初级">初级</option>
            <option value="中级">中级</option>
            <option value="高级">高级</option>
            <option value="专家">专家</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-white mb-2">
            所属城市 <span class="text-red-400">*</span>
          </label>
          <select
            v-model="form.cityName"
            class="select-gemini w-full"
            required
          >
            <option value="">请选择城市</option>
            <option v-for="city in cities" :key="city.id" :value="city.city_name">
              {{ city.city_name }}
            </option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            月工资 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.monthlySalary"
            type="number"
            step="0.01"
            min="0"
            class="input-gemini w-full"
            placeholder="请输入月工资"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-white mb-2">
            日工资 <span class="text-red-400">*</span>
          </label>
          <input
            v-model="form.dailySalary"
            type="number"
            step="0.01"
            min="0"
            class="input-gemini w-full"
            placeholder="请输入日工资"
            required
          />
          <p class="text-xs text-white/50 mt-1">
            建议日工资 = 月工资 ÷ 22天
          </p>
        </div>
        
        <!-- 自动计算建议 -->
        <div v-if="form.monthlySalary" class="glass border border-white/10 rounded-lg p-3">
          <div class="text-sm text-white/70 mb-2">建议日工资</div>
          <div class="text-sm text-white">
            基于月工资 ¥{{ parseFloat(form.monthlySalary).toFixed(2) }}
          </div>
          <div class="text-lg font-semibold text-white">
            ¥{{ (parseFloat(form.monthlySalary) / 22).toFixed(2) }}/天
          </div>
          <button
            type="button"
            @click="form.dailySalary = (parseFloat(form.monthlySalary) / 22).toFixed(2)"
            class="text-xs text-blue-400 hover:text-blue-300 mt-1"
          >
            使用建议值
          </button>
        </div>

        <!-- 预览 -->
        <div class="glass border border-white/10 rounded-lg p-3">
          <div class="text-sm text-white/70 mb-2">预览</div>
          <div class="text-sm text-white">
            <div>岗位：{{ form.positionName || '未填写' }} ({{ form.cityName || '未选择' }})</div>
            <div>级别：{{ form.positionLevel || '未选择' }}</div>
            <div>月薪：¥{{ parseFloat(form.monthlySalary || 0).toFixed(2) }}</div>
            <div>日薪：¥{{ parseFloat(form.dailySalary || 0).toFixed(2) }}</div>
          </div>
        </div>

        <div class="flex space-x-3 pt-4 sticky bottom-0 bg-transparent z-10">
          <button
            type="button"
            @click="$emit('close')"
            class="flex-1 glass-card px-4 py-2 text-white hover:bg-white/10 transition-all"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="loading || !canSubmit"
            class="flex-1 btn-gemini disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              保存中...
            </span>
            <span v-else>保存</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'

export default {
  name: 'AddPositionModal',
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()
    
    const form = ref({
      positionName: '',
      positionLevel: '',
      cityName: '',
      monthlySalary: '',
      dailySalary: ''
    })

    const cities = computed(() => quotationStore.cities)
    
    const loading = ref(false)
    
    const canSubmit = computed(() => {
      return form.value.positionName.trim() &&
             form.value.cityName.trim() &&
             form.value.positionLevel.trim() &&
             form.value.monthlySalary !== '' &&
             form.value.dailySalary !== '' &&
             parseFloat(form.value.monthlySalary) >= 0 &&
             parseFloat(form.value.dailySalary) >= 0
    })
    
    const handleSubmit = async () => {
      if (!canSubmit.value) {
        notificationStore.warning('请填写完整的岗位信息')
        return
      }
      
      try {
        loading.value = true
        
        const result = await quotationStore.addPosition({
          position_name: form.value.positionName.trim(),
          position_level: form.value.positionLevel.trim(),
          city_name: form.value.cityName.trim(),
          monthly_salary: parseFloat(form.value.monthlySalary),
          daily_salary: parseFloat(form.value.dailySalary)
        })
        
        if (result.success) {
          emit('success')
          emit('close')
        } else {
          notificationStore.error(result.message || '添加失败')
        }
      } catch (error) {
        console.error('添加岗位失败:', error)
        notificationStore.error('添加失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    onMounted(async () => {
      if (cities.value.length === 0) {
        await quotationStore.fetchCities()
      }
    })

    return {
      form,
      cities,
      loading,
      canSubmit,
      handleSubmit
    }
  }
}
</script>
