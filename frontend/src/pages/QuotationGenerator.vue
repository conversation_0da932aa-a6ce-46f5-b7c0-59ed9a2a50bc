<template>
  <div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold gradient-text mb-4">智能报价生成</h1>
        <p class="text-xl text-white/70">AI驱动的精准成本计算</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
        <!-- 左侧参数设置 -->
        <div class="lg:col-span-2">
          <div class="glass-card p-6 sticky top-24">
            <h2 class="text-2xl font-semibold text-white mb-6 flex items-center">
              <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
              </svg>
              报价参数
            </h2>

            <form @submit.prevent="generateQuotation" class="space-y-6">
              <!-- 商机名称 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  商机名称 <span class="text-red-400">*</span>
                </label>
                <select
                  v-model="form.opportunityName"
                  class="select-gemini w-full"
                  required
                >
                  <option value="">请选择商机项目</option>
                  <option v-for="opportunity in opportunities" :key="opportunity.id" :value="opportunity.name">
                    {{ opportunity.name }}
                  </option>
                </select>
                <p class="text-xs text-white/50 mt-1">
                  如需添加新商机，请前往数据管理页面
                </p>
              </div>

              <!-- 计算类型 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  计算类型 <span class="text-red-400">*</span>
                </label>
                <div class="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    @click="form.calculationType = 'monthly'"
                    class="p-3 rounded-lg border transition-all duration-300"
                    :class="form.calculationType === 'monthly' 
                      ? 'border-blue-500 bg-blue-500/20 text-white' 
                      : 'border-white/20 bg-white/5 text-white/70 hover:bg-white/10'"
                  >
                    <div class="text-center">
                      <div class="text-lg font-semibold">按月</div>
                      <div class="text-xs">月度成本计算</div>
                    </div>
                  </button>
                  <button
                    type="button"
                    @click="form.calculationType = 'daily'"
                    class="p-3 rounded-lg border transition-all duration-300"
                    :class="form.calculationType === 'daily' 
                      ? 'border-blue-500 bg-blue-500/20 text-white' 
                      : 'border-white/20 bg-white/5 text-white/70 hover:bg-white/10'"
                  >
                    <div class="text-center">
                      <div class="text-lg font-semibold">按天</div>
                      <div class="text-xs">日度成本计算</div>
                    </div>
                  </button>
                </div>
              </div>

              <!-- 工期设置 -->
              <div v-if="form.calculationType === 'monthly'">
                <label class="block text-sm font-medium text-white mb-2">
                  工期（月） <span class="text-red-400">*</span>
                </label>
                <input
                  v-model="form.workDurationMonths"
                  type="number"
                  min="1"
                  class="input-gemini w-full"
                  placeholder="请输入工期月数"
                  required
                />
              </div>

              <div v-if="form.calculationType === 'daily'">
                <div>
                  <label class="block text-sm font-medium text-white mb-2">
                    工期（天） <span class="text-red-400">*</span>
                  </label>
                  <input
                    v-model="form.workDurationDays"
                    type="number"
                    min="1"
                    class="input-gemini w-full"
                    placeholder="请输入工期天数"
                    required
                  />
                </div>
              </div>

              <!-- 报价系数 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  报价系数 <span class="text-red-400">*</span>
                </label>
                <input
                  v-model="form.quotationCoefficient"
                  type="number"
                  step="0.01"
                  min="0.01"
                  class="input-gemini w-full"
                  placeholder="请输入报价系数（如1.20）"
                  required
                />
                <p class="text-xs text-white/50 mt-1">
                  报价系数用于调整最终报价，支持两位小数，如1.20表示在基础成本上增加20%
                </p>
              </div>

              <!-- 城市选择 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  选择城市 <span class="text-red-400">*</span>
                </label>
                <div class="max-h-48 overflow-y-auto glass border border-white/10 rounded-lg p-3 space-y-2">
                  <div v-for="city in cities" :key="city.id" class="flex items-center">
                    <input
                      :id="`city-${city.id}`"
                      v-model="form.selectedCities"
                      :value="city.city_name"
                      type="checkbox"
                      class="checkbox-gemini mr-3"
                    />
                    <label :for="`city-${city.id}`" class="flex-1 text-sm text-white cursor-pointer">
                      {{ city.city_name }}
                      <span class="text-white/50 text-xs block">
                        社保基数: ¥{{ city.social_base.toFixed(2) }} |
                        公司缴纳: ¥{{ city.company_amount.toFixed(2) }}
                      </span>
                    </label>
                  </div>
                </div>
                <p v-if="!cities.length" class="text-sm text-white/50 mt-2">
                  暂无城市数据，请先在数据管理中添加
                </p>
              </div>

              <!-- 岗位选择 -->
              <div>
                <label class="block text-sm font-medium text-white mb-2">
                  选择岗位 <span class="text-red-400">*</span>
                </label>
                <div class="max-h-48 overflow-y-auto glass border border-white/10 rounded-lg p-3 space-y-2">
                  <div v-for="position in positions" :key="position.id" class="flex items-center">
                    <input
                      :id="`position-${position.id}`"
                      v-model="form.selectedPositions"
                      :value="position.position_name"
                      type="checkbox"
                      class="checkbox-gemini mr-3"
                    />
                    <label :for="`position-${position.id}`" class="flex-1 text-sm text-white cursor-pointer">
                      {{ position.position_name }}
                      <span class="text-white/50 text-xs block">
                        <span v-if="form.calculationType === 'monthly'">
                          月薪: ¥{{ position.monthly_salary?.toFixed(2) || '0.00' }}
                        </span>
                        <span v-else>
                          日薪: ¥{{ position.daily_salary?.toFixed(2) || '0.00' }}
                        </span>
                      </span>
                    </label>
                  </div>
                </div>
                <p v-if="!positions.length" class="text-sm text-white/50 mt-2">
                  暂无岗位数据，请先在数据管理中添加
                </p>
              </div>

              <!-- 生成按钮 -->
              <button
                type="submit"
                :disabled="loading || !canGenerate"
                class="btn-gemini w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="loading" class="animate-spin w-5 h-5 mr-2 inline" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                </svg>
                {{ loading ? '生成中...' : '生成报价' }}
              </button>
            </form>
          </div>
        </div>

        <!-- 右侧结果展示 -->
        <div class="lg:col-span-3">
          <div class="glass-card p-6">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                报价明细表
              </h2>
              <button
                v-if="quotationResult"
                @click="exportQuotation"
                class="btn-gemini px-4 py-2 text-sm"
              >
                <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                导出Excel
              </button>
            </div>

            <!-- 结果展示区域 -->
            <div v-if="!quotationResult" class="text-center py-16">
              <svg class="w-24 h-24 mx-auto text-white/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <p class="text-white/50 text-lg">请设置报价参数并点击"生成报价"</p>
            </div>

            <!-- 报价结果 -->
            <div v-else class="space-y-6">
              <!-- 基本信息 -->
              <div class="glass border border-white/10 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-white mb-3">基本信息</h3>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-white/70">商机名称：</span>
                    <span class="text-white">{{ quotationResult.opportunity_name }}</span>
                  </div>
                  <div>
                    <span class="text-white/70">计算类型：</span>
                    <span class="text-white">{{ quotationResult.calculation_type === 'monthly' ? '按月' : '按天' }}</span>
                  </div>
                  <div v-if="quotationResult.calculation_type === 'monthly'">
                    <span class="text-white/70">工期：</span>
                    <span class="text-white">{{ quotationResult.work_duration_months }}个月</span>
                  </div>
                  <div v-else>
                    <span class="text-white/70">工期：</span>
                    <span class="text-white">{{ quotationResult.work_duration_days }}天</span>
                  </div>
                  <div>
                    <span class="text-white/70">报价系数：</span>
                    <span class="text-white">{{ quotationResult.quotation_coefficient }}</span>
                  </div>
                  <div>
                    <span class="text-white/70">总金额：</span>
                    <span class="text-2xl font-bold gradient-text">¥{{ quotationResult.total_amount.toFixed(2) }}</span>
                  </div>
                </div>
              </div>

              <!-- 明细表格 -->
              <div class="overflow-x-auto">
                <table class="w-full text-sm">
                  <thead>
                    <tr class="border-b border-white/20">
                      <th class="text-left py-3 px-2 text-white/70">城市</th>
                      <th class="text-left py-3 px-2 text-white/70">岗位</th>
                      <th class="text-left py-3 px-2 text-white/70" v-if="quotationResult.calculation_type === 'monthly'">月工资</th>
                      <th class="text-left py-3 px-2 text-white/70" v-else>日工资</th>
                      <th class="text-left py-3 px-2 text-white/70">社保基数</th>
                      <th class="text-left py-3 px-2 text-white/70">公司社保</th>
                      <th class="text-left py-3 px-2 text-white/70">报价系数</th>
                      <th class="text-left py-3 px-2 text-white/70">小计</th>
                    </tr>
                  </thead>
                  <tbody>
                    <template v-for="(cityGroup, cityName) in groupedDetails" :key="cityName">
                      <!-- 城市明细行 -->
                      <tr v-for="(detail, index) in cityGroup.details" :key="`${cityName}-${index}`"
                          class="border-b border-white/10 hover:bg-white/5 transition-colors">
                        <td class="py-3 px-2 text-white">{{ detail.city_name }}</td>
                        <td class="py-3 px-2 text-white">{{ detail.position_name }}</td>
                        <td class="py-3 px-2 text-white" v-if="quotationResult.calculation_type === 'monthly'">
                          ¥{{ detail.monthly_salary?.toFixed(2) || '0.00' }}
                        </td>
                        <td class="py-3 px-2 text-white" v-else>
                          ¥{{ detail.daily_salary?.toFixed(2) || '0.00' }}
                        </td>
                        <td class="py-3 px-2 text-white">¥{{ detail.social_base?.toFixed(2) || '0.00' }}</td>
                        <td class="py-3 px-2 text-white">¥{{ detail.company_insurance?.toFixed(2) || '0.00' }}</td>
                        <td class="py-3 px-2 text-white">{{ detail.quotation_coefficient?.toFixed(2) || '1.00' }}</td>
                        <td class="py-3 px-2 text-white font-semibold">¥{{ detail.subtotal?.toFixed(2) || '0.00' }}</td>
                      </tr>
                      <!-- 城市汇总行 -->
                      <tr class="bg-blue-500/10 border-b-2 border-blue-500/30">
                        <td class="py-2 px-2 text-blue-300 font-semibold">{{ cityName }} 小计</td>
                        <td colspan="6" class="py-2 px-2"></td>
                        <td class="py-2 px-2 text-blue-300 font-bold">¥{{ cityGroup.total.toFixed(2) }}</td>
                      </tr>
                    </template>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'
import { useNotificationStore } from '../stores/notification'

export default {
  name: 'QuotationGenerator',
  setup() {
    const quotationStore = useQuotationStore()
    const notificationStore = useNotificationStore()

    // 响应式数据
    const form = ref({
      opportunityName: '',
      calculationType: 'monthly',
      workDurationMonths: 1,
      workDurationDays: 30,
      quotationCoefficient: 1.00,
      selectedCities: [],
      selectedPositions: []
    })

    const quotationResult = ref(null)
    const loading = ref(false)

    // 计算属性
    const cities = computed(() => quotationStore.cities)
    const positions = computed(() => quotationStore.positions)
    const opportunities = computed(() => quotationStore.opportunities)

    const canGenerate = computed(() => {
      return form.value.opportunityName.trim() &&
             form.value.selectedCities.length > 0 &&
             form.value.selectedPositions.length > 0 &&
             form.value.quotationCoefficient > 0 &&
             (form.value.calculationType === 'monthly' ?
               form.value.workDurationMonths > 0 :
               form.value.workDurationDays > 0)
    })

    // 按城市分组的报价明细
    const groupedDetails = computed(() => {
      if (!quotationResult.value || !quotationResult.value.details) {
        return {}
      }

      const groups = {}
      quotationResult.value.details.forEach(detail => {
        const cityName = detail.city_name
        if (!groups[cityName]) {
          groups[cityName] = {
            details: [],
            total: 0
          }
        }
        groups[cityName].details.push(detail)
        groups[cityName].total += detail.subtotal
      })

      return groups
    })

    // 方法
    const generateQuotation = async () => {
      if (!canGenerate.value) {
        notificationStore.warning('请填写完整的报价参数')
        return
      }

      try {
        loading.value = true

        const quotationData = {
          opportunity_name: form.value.opportunityName.trim(),
          calculation_type: form.value.calculationType,
          selected_cities: form.value.selectedCities,
          selected_positions: form.value.selectedPositions,
          quotation_coefficient: parseFloat(form.value.quotationCoefficient)
        }

        if (form.value.calculationType === 'monthly') {
          quotationData.work_duration_months = parseInt(form.value.workDurationMonths)
        } else {
          quotationData.work_duration_days = parseInt(form.value.workDurationDays)
        }

        const result = await quotationStore.generateQuotation(quotationData)

        if (result.success) {
          quotationResult.value = {
            ...result,
            opportunity_name: form.value.opportunityName,
            calculation_type: form.value.calculationType,
            work_duration_months: form.value.calculationType === 'monthly' ? form.value.workDurationMonths : null,
            work_duration_days: form.value.calculationType === 'daily' ? form.value.workDurationDays : null,
            quotation_coefficient: form.value.quotationCoefficient
          }
          notificationStore.success('报价生成成功！')
        } else {
          notificationStore.error(result.message || '报价生成失败')
        }
      } catch (error) {
        console.error('生成报价失败:', error)
        notificationStore.error('报价生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    const exportQuotation = () => {
      if (quotationResult.value && quotationResult.value.quotation_id) {
        quotationStore.exportQuotation(quotationResult.value.quotation_id)
        notificationStore.success('报价表导出成功！')
      }
    }

    const loadData = async () => {
      try {
        await quotationStore.initializeData()
      } catch (error) {
        console.error('加载数据失败:', error)
        notificationStore.error('加载数据失败')
      }
    }

    // 生命周期
    onMounted(() => {
      loadData()
    })

    return {
      form,
      quotationResult,
      loading,
      cities,
      positions,
      opportunities,
      canGenerate,
      groupedDetails,
      generateQuotation,
      exportQuotation
    }
  }
}
</script>

<style scoped>
/* 自定义样式 */
.checkbox-gemini:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4285f4, #9c27b0);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9c27b0, #e91e63);
}
</style>
