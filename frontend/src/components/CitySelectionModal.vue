<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="$emit('close')"></div>
    
    <!-- 模态框内容 -->
    <div class="relative glass-card p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-semibold text-white">选择城市</h3>
        <button @click="$emit('close')" class="text-white/70 hover:text-white">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      
      <!-- 搜索框 -->
      <div class="mb-6">
        <div class="relative">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索城市名称..."
            class="input-gemini pl-10 pr-4 py-2 w-full"
          />
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
          </svg>
        </div>
      </div>

      <!-- 已选择的城市 -->
      <div v-if="selectedCities.length > 0" class="mb-6">
        <h4 class="text-lg font-semibold text-white mb-3">已选择 ({{ selectedCities.length }})</h4>
        <div class="flex flex-wrap gap-2">
          <div
            v-for="city in selectedCities"
            :key="city.id"
            class="flex items-center bg-blue-500/20 border border-blue-500/30 rounded-lg px-3 py-2"
          >
            <span class="text-blue-300 mr-2">{{ city.city_name }}</span>
            <button
              @click="removeCity(city)"
              class="text-blue-300 hover:text-white"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 城市列表 -->
      <div class="mb-6">
        <h4 class="text-lg font-semibold text-white mb-3">可选城市</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-96 overflow-y-auto">
          <div
            v-for="city in filteredCities"
            :key="city.id"
            @click="toggleCity(city)"
            class="glass border border-white/10 rounded-lg p-4 cursor-pointer transition-all duration-300 hover:bg-white/10"
            :class="{ 'border-blue-500 bg-blue-500/20': isSelected(city) }"
          >
            <div class="flex items-center justify-between">
              <div>
                <h5 class="font-semibold text-white">{{ city.city_name }}</h5>
                <div class="text-sm text-white/70 mt-1">
                  <div>社保基数: ¥{{ city.social_base.toFixed(2) }}</div>
                  <div>公司缴纳: ¥{{ city.company_amount.toFixed(2) }}</div>
                </div>
              </div>
              <div class="ml-4">
                <div
                  class="w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all"
                  :class="isSelected(city) 
                    ? 'border-blue-500 bg-blue-500' 
                    : 'border-white/30'"
                >
                  <svg v-if="isSelected(city)" class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-3">
        <button
          @click="$emit('close')"
          class="glass-card px-6 py-2 text-white hover:bg-white/10 transition-all"
        >
          取消
        </button>
        <button
          @click="confirmSelection"
          class="btn-gemini px-6 py-2"
          :disabled="selectedCities.length === 0"
        >
          确认选择 ({{ selectedCities.length }})
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuotationStore } from '../stores/quotation'

export default {
  name: 'CitySelectionModal',
  props: {
    initialSelected: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'confirm'],
  setup(props, { emit }) {
    const quotationStore = useQuotationStore()
    
    const searchQuery = ref('')
    const selectedCities = ref([...props.initialSelected])
    
    const cities = computed(() => quotationStore.cities)
    
    const filteredCities = computed(() => {
      if (!searchQuery.value) return cities.value
      
      const query = searchQuery.value.toLowerCase()
      return cities.value.filter(city => 
        city.city_name.toLowerCase().includes(query)
      )
    })
    
    const isSelected = (city) => {
      return selectedCities.value.some(selected => selected.id === city.id)
    }
    
    const toggleCity = (city) => {
      if (isSelected(city)) {
        removeCity(city)
      } else {
        selectedCities.value.push(city)
      }
    }
    
    const removeCity = (city) => {
      const index = selectedCities.value.findIndex(selected => selected.id === city.id)
      if (index > -1) {
        selectedCities.value.splice(index, 1)
      }
    }
    
    const confirmSelection = () => {
      emit('confirm', selectedCities.value)
      emit('close')
    }
    
    onMounted(async () => {
      try {
        // 强制重新加载城市数据
        await quotationStore.fetchCities()
      } catch (error) {
        console.error('加载城市数据失败:', error)
      }
    })
    
    return {
      searchQuery,
      selectedCities,
      cities,
      filteredCities,
      isSelected,
      toggleCity,
      removeCity,
      confirmSelection
    }
  }
}
</script>

<style scoped>
/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4285f4, #9c27b0);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9c27b0, #e91e63);
}
</style>
