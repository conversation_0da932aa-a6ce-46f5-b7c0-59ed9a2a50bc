#!/usr/bin/env python3
"""
测试岗位数据修复的脚本
"""
import requests
import json

def test_positions_api():
    """测试岗位API"""
    base_url = "http://localhost:5001"
    
    print("🧪 测试岗位数据API...")
    print("=" * 50)
    
    # 1. 测试后端API
    print("\n1. 测试后端API")
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端API响应成功")
            print(f"   - 响应格式: {type(data)}")
            print(f"   - 包含字段: {list(data.keys())}")
            
            if 'positions' in data:
                positions = data['positions']
                print(f"   - 岗位数量: {len(positions)}")
                print(f"   - 总数: {data.get('total', 'N/A')}")
                
                # 显示前3个岗位
                for i, pos in enumerate(positions[:3]):
                    print(f"   岗位 {i+1}: {pos['position_name']} - {pos['city_name']} - ¥{pos['monthly_salary']}")
            else:
                print(f"   - 直接数组格式，岗位数量: {len(data)}")
        else:
            print(f"❌ 后端API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 后端API异常: {e}")
    
    # 2. 测试前端能否正确解析
    print("\n2. 测试数据结构兼容性")
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            data = response.json()
            
            # 模拟前端的处理逻辑
            if 'positions' in data:
                positions = data['positions']
                print(f"✅ 分页格式处理成功，获取到 {len(positions)} 个岗位")
            else:
                positions = data
                print(f"✅ 直接数组格式处理成功，获取到 {len(positions)} 个岗位")
            
            # 验证数据结构
            if positions and len(positions) > 0:
                first_pos = positions[0]
                required_fields = ['id', 'position_name', 'city_name', 'monthly_salary', 'daily_salary']
                missing_fields = [field for field in required_fields if field not in first_pos]
                
                if not missing_fields:
                    print(f"✅ 数据结构完整，包含所有必需字段")
                else:
                    print(f"❌ 缺少字段: {missing_fields}")
            else:
                print(f"❌ 没有岗位数据")
                
    except Exception as e:
        print(f"❌ 数据结构测试异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试岗位数据修复...")
    test_positions_api()
    
    print("\n" + "=" * 50)
    print("✨ 测试完成！")
    print("\n📋 修复说明:")
    print("1. 后端API现在返回分页格式: {positions: [...], total: N}")
    print("2. 前端代码已更新，兼容分页和直接数组两种格式")
    print("3. 岗位数据包含城市字段，支持城市-岗位关联")
    
    print("\n🌐 访问地址:")
    print("- 前端界面: http://localhost:3000")
    print("- 后端API: http://localhost:5001")
    print("\n💡 建议:")
    print("- 访问前端数据管理页面查看岗位列表")
    print("- 检查岗位数据是否正常显示")

if __name__ == '__main__':
    main()
