#!/usr/bin/env python3
"""
初始化数据库脚本
"""
from app import app, db, Opportunity, CitySocialInsurance, PositionCost

def init_database():
    """初始化数据库"""
    with app.app_context():
        # 删除所有表
        db.drop_all()
        
        # 创建所有表
        db.create_all()
        
        print("数据库表创建成功！")
        
        # 添加示例数据
        add_sample_data()

def add_sample_data():
    """添加示例数据"""
    # 添加示例商机
    opportunities = [
        {'name': '某大型企业人力资源外包项目', 'description': '为某大型制造企业提供全国多地人力资源外包服务'},
        {'name': '互联网公司技术团队外包', 'description': '为互联网公司提供技术开发团队外包服务'},
        {'name': '金融机构客服中心项目', 'description': '为银行提供客服中心人员外包服务'},
    ]
    
    for opp_data in opportunities:
        opportunity = Opportunity(**opp_data)
        db.session.add(opportunity)
    
    # 添加示例城市数据
    cities = [
        {'city_name': '北京', 'social_base': 5000.00, 'social_rate': 0.16, 'housing_base': 5000.00, 'housing_rate': 0.12, 'company_amount': 1400.00},
        {'city_name': '上海', 'social_base': 4800.00, 'social_rate': 0.16, 'housing_base': 4800.00, 'housing_rate': 0.12, 'company_amount': 1344.00},
        {'city_name': '广州', 'social_base': 4200.00, 'social_rate': 0.16, 'housing_base': 4200.00, 'housing_rate': 0.12, 'company_amount': 1176.00},
        {'city_name': '深圳', 'social_base': 4600.00, 'social_rate': 0.16, 'housing_base': 4600.00, 'housing_rate': 0.12, 'company_amount': 1288.00},
        {'city_name': '杭州', 'social_base': 4000.00, 'social_rate': 0.16, 'housing_base': 4000.00, 'housing_rate': 0.12, 'company_amount': 1120.00},
    ]
    
    for city_data in cities:
        city = CitySocialInsurance(**city_data)
        db.session.add(city)
    
    # 添加示例岗位数据（每个岗位在每个城市都有数据）
    cities_list = ['北京', '上海', '广州', '深圳', '杭州']
    base_positions = [
        {'position_name': '软件开发工程师', 'position_level': '', 'monthly_salary': 15000.00, 'daily_salary': 682.00},
        {'position_name': '项目经理', 'position_level': '', 'monthly_salary': 20000.00, 'daily_salary': 909.00},
        {'position_name': '测试工程师', 'position_level': '', 'monthly_salary': 12000.00, 'daily_salary': 545.00},
        {'position_name': '产品经理', 'position_level': '', 'monthly_salary': 18000.00, 'daily_salary': 818.00},
        {'position_name': 'UI设计师', 'position_level': '', 'monthly_salary': 13000.00, 'daily_salary': 591.00},
        {'position_name': '运维工程师', 'position_level': '', 'monthly_salary': 14000.00, 'daily_salary': 636.00},
        {'position_name': '客服专员', 'position_level': '', 'monthly_salary': 8000.00, 'daily_salary': 364.00},
        {'position_name': '销售代表', 'position_level': '', 'monthly_salary': 10000.00, 'daily_salary': 455.00},
    ]

    positions = []
    for city in cities_list:
        for base_pos in base_positions:
            # 根据城市调整工资水平
            city_multiplier = {
                '北京': 1.2, '上海': 1.15, '深圳': 1.1,
                '杭州': 1.05, '广州': 1.0
            }.get(city, 1.0)

            positions.append({
                'position_name': base_pos['position_name'],
                'position_level': base_pos.get('position_level', ''),
                'city_name': city,
                'monthly_salary': base_pos['monthly_salary'] * city_multiplier,
                'daily_salary': base_pos['daily_salary'] * city_multiplier
            })
    
    for pos_data in positions:
        position = PositionCost(**pos_data)
        db.session.add(position)
    
    db.session.commit()
    print("示例数据添加成功！")

if __name__ == '__main__':
    init_database()
