from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List
import json
from ..models.database import get_db
from ..models.schemas import MatchRequest, MatchResult
from ..services.match_service import MatchService

router = APIRouter(prefix="/matches", tags=["matches"])


@router.post("/analyze")
async def analyze_matches(
    request: MatchRequest,
    db: Session = Depends(get_db)
):
    """分析简历匹配度（SSE流式输出）"""
    match_service = MatchService(db)
    
    async def event_stream():
        try:
            async for result in match_service.analyze_matches(
                job_id=request.job_id,
                resume_ids=request.resume_ids
            ):
                # 格式化为SSE格式
                data = json.dumps(result, ensure_ascii=False)
                yield f"data: {data}\n\n"
        except Exception as e:
            error_data = {
                "type": "error",
                "message": f"分析过程中出现错误: {str(e)}"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        finally:
            # 发送结束信号
            yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@router.get("/results", response_model=List[MatchResult])
async def get_match_results(
    job_id: int = None,
    resume_id: int = None,
    db: Session = Depends(get_db)
):
    """获取匹配结果"""
    match_service = MatchService(db)
    return match_service.get_match_results(job_id=job_id, resume_id=resume_id)


@router.get("/summary/{job_id}")
async def get_match_summary(job_id: int, db: Session = Depends(get_db)):
    """获取匹配结果摘要"""
    match_service = MatchService(db)
    return match_service.get_match_summary(job_id)


@router.post("/export/word")
async def export_matches_to_word(
    request: MatchRequest,
    db: Session = Depends(get_db)
):
    """导出匹配结果为Word文档"""
    match_service = MatchService(db)
    
    # 获取匹配结果
    match_results = match_service.get_match_results(job_id=request.job_id)
    
    if not match_results:
        raise HTTPException(status_code=404, detail="未找到匹配结果")
    
    # 过滤指定的简历
    if request.resume_ids:
        match_results = [
            m for m in match_results 
            if m.resume_id in request.resume_ids
        ]
    
    if not match_results:
        raise HTTPException(status_code=404, detail="未找到指定简历的匹配结果")
    
    # 生成Word文档
    word_content = match_service.export_to_word(match_results)
    
    return Response(
        content=word_content,
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        headers={
            "Content-Disposition": "attachment; filename=match_results.docx"
        }
    )


@router.post("/export/pdf")
async def export_matches_to_pdf(
    request: MatchRequest,
    db: Session = Depends(get_db)
):
    """导出匹配结果为PDF文档"""
    match_service = MatchService(db)

    try:
        # 生成PDF文档
        pdf_content = await match_service.export_results_to_pdf(request.job_id, request.resume_ids)

        # 获取岗位信息用于文件名
        job = db.query(Job).filter(Job.id == request.job_id).first()
        job_title = job.title if job else "未知岗位"

        # 生成文件名
        filename = f"匹配分析结果_{job_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )

    except Exception as e:
        logger.error(f"PDF导出失败: {e}")
        raise HTTPException(status_code=500, detail=f"PDF导出失败: {str(e)}")
