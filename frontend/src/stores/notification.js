import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useNotificationStore = defineStore('notification', () => {
  const notification = ref({
    show: false,
    type: 'info', // success, error, warning, info
    message: '',
    icon: 'InformationCircleIcon'
  })

  const show = (type, message, duration = 3000) => {
    const icons = {
      success: 'CheckCircleIcon',
      error: 'XCircleIcon',
      warning: 'ExclamationTriangleIcon',
      info: 'InformationCircleIcon'
    }

    notification.value = {
      show: true,
      type,
      message,
      icon: icons[type] || icons.info
    }

    if (duration > 0) {
      setTimeout(() => {
        hide()
      }, duration)
    }
  }

  const hide = () => {
    notification.value.show = false
  }

  const success = (message, duration) => show('success', message, duration)
  const error = (message, duration) => show('error', message, duration)
  const warning = (message, duration) => show('warning', message, duration)
  const info = (message, duration) => show('info', message, duration)

  return {
    notification,
    show,
    hide,
    success,
    error,
    warning,
    info
  }
})
