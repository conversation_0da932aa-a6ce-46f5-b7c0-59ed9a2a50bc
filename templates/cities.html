{% extends "base.html" %}

{% block title %}城市社保公积金管理 - 商机报价系统{% endblock %}

{% block content %}
<div id="alerts"></div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">城市社保公积金管理</h5>
                <div>
                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="bi bi-upload"></i> 导入Excel
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCityModal">
                        <i class="bi bi-plus"></i> 添加城市
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if cities %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>城市名称</th>
                                <th>社保基数</th>
                                <th>公司缴纳金额</th>
                                <th>更新时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for city in cities %}
                            <tr>
                                <td>{{ city.city_name }}</td>
                                <td>¥{{ "%.2f"|format(city.social_base) }}</td>
                                <td>¥{{ "%.2f"|format(city.company_amount) }}</td>
                                <td>{{ city.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-building display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无城市数据</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCityModal">
                        添加第一个城市
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 添加城市模态框 -->
<div class="modal fade" id="addCityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加/更新城市数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="cityForm">
                    <div class="mb-3">
                        <label for="cityName" class="form-label">城市名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="cityName" required>
                    </div>
                    <div class="mb-3">
                        <label for="socialBase" class="form-label">社保基数 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="socialBase" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="companyAmount" class="form-label">公司缴纳金额 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="companyAmount" step="0.01" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addCity()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 导入Excel模态框 -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入Excel文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <strong>Excel文件格式要求：</strong><br>
                    必须包含以下列：<br>
                    • 城市名称<br>
                    • 社保基数<br>
                    • 公司缴纳金额
                </div>
                <form id="importForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="importCities()">导入</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addCity() {
    const cityName = $('#cityName').val().trim();
    const socialBase = $('#socialBase').val();
    const companyAmount = $('#companyAmount').val();
    
    if (!cityName || !socialBase || !companyAmount) {
        showAlert('请填写所有必填字段', 'danger');
        return;
    }
    
    const data = {
        city_name: cityName,
        social_base: parseFloat(socialBase),
        company_amount: parseFloat(companyAmount)
    };
    
    submitForm('/api/cities', data, function(response) {
        $('#addCityModal').modal('hide');
        $('#cityForm')[0].reset();
        setTimeout(() => {
            location.reload();
        }, 1000);
    });
}

function importCities() {
    const fileInput = $('#importFile')[0];
    if (!fileInput.files.length) {
        showAlert('请选择文件', 'danger');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    
    $.ajax({
        url: '/api/import_cities',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                $('#importModal').modal('hide');
                $('#importForm')[0].reset();
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('导入失败，请稍后重试', 'danger');
        }
    });
}

// 表单提交事件
$('#cityForm').on('submit', function(e) {
    e.preventDefault();
    addCity();
});
</script>
{% endblock %}
