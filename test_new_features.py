#!/usr/bin/env python3
"""
测试新功能的脚本
"""
import requests
import json

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5000"
    
    print("🧪 测试API端点...")
    
    # 测试获取商机列表
    try:
        response = requests.get(f"{base_url}/api/opportunities")
        if response.status_code == 200:
            opportunities = response.json()
            print(f"✅ 商机列表获取成功，共 {len(opportunities)} 个商机")
            for opp in opportunities:
                print(f"   - {opp['name']}")
        else:
            print(f"❌ 商机列表获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 商机列表获取异常: {e}")
    
    # 测试获取城市列表
    try:
        response = requests.get(f"{base_url}/api/cities")
        if response.status_code == 200:
            cities = response.json()
            print(f"✅ 城市列表获取成功，共 {len(cities)} 个城市")
            for city in cities:
                print(f"   - {city['city_name']}: 社保基数¥{city['social_base']}, 公司缴纳¥{city['company_amount']}")
        else:
            print(f"❌ 城市列表获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 城市列表获取异常: {e}")
    
    # 测试获取岗位列表
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            positions = response.json()
            print(f"✅ 岗位列表获取成功，共 {len(positions)} 个岗位")
            for pos in positions:
                print(f"   - {pos['position_name']}: 月薪¥{pos['monthly_salary']}, 日薪¥{pos['daily_salary']}")
        else:
            print(f"❌ 岗位列表获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 岗位列表获取异常: {e}")

def test_quotation_generation():
    """测试报价生成功能"""
    base_url = "http://localhost:5000"
    
    print("\n🧪 测试报价生成功能...")
    
    # 测试按月计算
    monthly_data = {
        "opportunity_name": "某大型企业人力资源外包项目",
        "calculation_type": "monthly",
        "work_duration_months": 6,
        "selected_cities": ["北京", "上海"],
        "selected_positions": ["软件开发工程师", "项目经理"]
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate_quotation",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(monthly_data)
        )
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 按月计算报价生成成功")
                print(f"   - 报价ID: {result['quotation_id']}")
                print(f"   - 总金额: ¥{result['total_amount']:.2f}")
                print(f"   - 明细数量: {len(result['details'])}")
            else:
                print(f"❌ 按月计算报价生成失败: {result['message']}")
        else:
            print(f"❌ 按月计算报价生成请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 按月计算报价生成异常: {e}")
    
    # 测试按天计算（使用报价系数）
    daily_data = {
        "opportunity_name": "互联网公司技术团队外包",
        "calculation_type": "daily",
        "work_duration_days": 90,
        "quotation_coefficient": 1.25,  # 使用报价系数
        "selected_cities": ["深圳", "杭州"],
        "selected_positions": ["UI设计师", "测试工程师"]
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate_quotation",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(daily_data)
        )
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 按天计算报价生成成功")
                print(f"   - 报价ID: {result['quotation_id']}")
                print(f"   - 总金额: ¥{result['total_amount']:.2f}")
                print(f"   - 报价系数: {daily_data['quotation_coefficient']}")
                print(f"   - 明细数量: {len(result['details'])}")
            else:
                print(f"❌ 按天计算报价生成失败: {result['message']}")
        else:
            print(f"❌ 按天计算报价生成请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 按天计算报价生成异常: {e}")

def test_quotation_records():
    """测试报价记录功能"""
    base_url = "http://localhost:5000"
    
    print("\n🧪 测试报价记录功能...")
    
    try:
        response = requests.get(f"{base_url}/api/quotation_records")
        if response.status_code == 200:
            records = response.json()
            print(f"✅ 报价记录获取成功，共 {len(records)} 条记录")
            for record in records:
                print(f"   - ID: {record['id']}, 商机: {record['opportunity_name']}")
                print(f"     类型: {record['calculation_type']}, 总金额: ¥{record['total_amount']:.2f}")
                if record['quotation_coefficient']:
                    print(f"     报价系数: {record['quotation_coefficient']}")
        else:
            print(f"❌ 报价记录获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 报价记录获取异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试新功能...")
    print("=" * 50)
    
    test_api_endpoints()
    test_quotation_generation()
    test_quotation_records()
    
    print("\n" + "=" * 50)
    print("✨ 测试完成！")
    print("\n📋 新功能说明:")
    print("1. 去掉个人社保录入和计算，改为社保基数显示")
    print("2. 去掉考勤基数，改为报价系数（支持两位小数）")
    print("3. 新计算公式：(岗位工资+公司社保公积金)*工期*报价系数")
    print("4. 商机名称改为下拉选择方式")
    print("5. 报价明细表按城市分组显示，每个城市有小计")
    print("6. 修复了明细表中工资显示问题")
    
    print("\n🌐 访问地址:")
    print("- 前端界面: http://localhost:3000")
    print("- 后端API: http://localhost:5000")

if __name__ == '__main__':
    main()
