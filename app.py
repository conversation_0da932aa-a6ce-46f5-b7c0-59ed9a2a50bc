from flask import Flask, render_template, request, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from datetime import datetime
import pandas as pd
from io import BytesIO
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///quotation_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 启用CORS
CORS(app)

db = SQLAlchemy(app)

# 添加自定义过滤器
@app.template_filter('from_json')
def from_json_filter(value):
    """将JSON字符串转换为Python对象"""
    try:
        return json.loads(value)
    except Exception:
        return []

# 数据库模型
class Opportunity(db.Model):
    """商机表"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class CitySocialInsurance(db.Model):
    """城市社保公积金表"""
    id = db.Column(db.Integer, primary_key=True)
    city_name = db.Column(db.String(100), nullable=False, unique=True)
    social_base = db.Column(db.Float, nullable=False)      # 社保基数
    social_rate = db.Column(db.Float, nullable=False, default=0.16)  # 社保缴纳比例（默认16%）
    housing_base = db.Column(db.Float, nullable=False)     # 公积金缴纳基数
    housing_rate = db.Column(db.Float, nullable=False, default=0.12)  # 公积金缴纳比例（默认12%）
    company_amount = db.Column(db.Float, nullable=False)   # 公司缴纳总额（自动计算）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class PositionCost(db.Model):
    """岗位成本表"""
    id = db.Column(db.Integer, primary_key=True)
    position_name = db.Column(db.String(100), nullable=False)
    city_name = db.Column(db.String(100), nullable=False)  # 城市名称
    position_level = db.Column(db.String(50), nullable=True)  # 岗位级别
    monthly_salary = db.Column(db.Float, nullable=False)  # 月工资
    daily_salary = db.Column(db.Float, nullable=False)    # 日工资
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 添加复合唯一约束
    __table_args__ = (db.UniqueConstraint('position_name', 'city_name', 'position_level', name='_position_city_level_uc'),)

class QuotationRecord(db.Model):
    """报价记录表"""
    id = db.Column(db.Integer, primary_key=True)
    opportunity_name = db.Column(db.String(200), nullable=False)
    selected_cities = db.Column(db.Text, nullable=False)  # JSON格式存储选中的城市
    selected_positions = db.Column(db.Text, nullable=False)  # JSON格式存储选中的岗位
    work_duration_months = db.Column(db.Integer, nullable=True)  # 工期（月）
    work_duration_days = db.Column(db.Integer, nullable=True)    # 工期（天）
    calculation_type = db.Column(db.String(10), nullable=False)  # 计算类型：monthly/daily
    quotation_coefficient = db.Column(db.Numeric(5, 2), nullable=True)  # 报价系数（支持两位小数）
    total_amount = db.Column(db.Float, nullable=False)  # 总金额
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关联报价明细
    details = db.relationship('QuotationDetail', backref='quotation', lazy=True, cascade='all, delete-orphan')

class QuotationDetail(db.Model):
    """报价明细表"""
    id = db.Column(db.Integer, primary_key=True)
    quotation_id = db.Column(db.Integer, db.ForeignKey('quotation_record.id'), nullable=False)
    city_name = db.Column(db.String(100), nullable=False)
    position_name = db.Column(db.String(100), nullable=False)
    position_level = db.Column(db.String(50), nullable=True)  # 岗位级别
    monthly_salary = db.Column(db.Float, nullable=True)     # 月工资
    daily_salary = db.Column(db.Float, nullable=True)       # 日工资
    social_base = db.Column(db.Float, nullable=False)       # 社保基数（仅显示）
    company_insurance = db.Column(db.Float, nullable=False)
    work_duration_months = db.Column(db.Integer, nullable=True)  # 工期（月）
    work_duration_days = db.Column(db.Integer, nullable=True)    # 工期（天）
    calculation_type = db.Column(db.String(10), nullable=False)  # 计算类型
    quotation_coefficient = db.Column(db.Numeric(5, 2), nullable=True)  # 报价系数（支持两位小数）
    subtotal = db.Column(db.Float, nullable=False)  # 小计金额

# 路由定义
@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/opportunities')
def opportunities():
    """商机管理页面"""
    opportunities = Opportunity.query.all()
    return render_template('opportunities.html', opportunities=opportunities)

@app.route('/api/opportunities', methods=['GET'])
def get_opportunities():
    """获取所有商机"""
    opportunities = Opportunity.query.all()
    return jsonify([{
        'id': opp.id,
        'name': opp.name,
        'description': opp.description,
        'created_at': opp.created_at.isoformat()
    } for opp in opportunities])

@app.route('/api/cities', methods=['GET'])
def get_cities():
    """获取所有城市"""
    cities = CitySocialInsurance.query.all()
    return jsonify([{
        'id': city.id,
        'city_name': city.city_name,
        'social_base': city.social_base,
        'social_rate': city.social_rate,
        'housing_base': city.housing_base,
        'housing_rate': city.housing_rate,
        'company_amount': city.company_amount,
        'created_at': city.created_at.isoformat(),
        'updated_at': city.updated_at.isoformat()
    } for city in cities])

@app.route('/api/positions', methods=['GET'])
def get_positions():
    """获取所有岗位，支持分页和多条件筛选"""
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    city_name = request.args.get('city_name', '').strip()
    position_name = request.args.get('position_name', '').strip()
    position_level = request.args.get('position_level', '').strip()

    query = PositionCost.query
    if city_name:
        query = query.filter(PositionCost.city_name.like(f"%{city_name}%"))
    if position_name:
        query = query.filter(PositionCost.position_name.like(f"%{position_name}%"))
    if position_level:
        query = query.filter(PositionCost.position_level.like(f"%{position_level}%"))

    total = query.count()
    positions = query.order_by(PositionCost.updated_at.desc()).offset((page-1)*page_size).limit(page_size).all()

    return jsonify({
        'total': total,
        'page': page,
        'page_size': page_size,
        'positions': [{
            'id': pos.id,
            'position_name': pos.position_name,
            'city_name': pos.city_name,
            'position_level': pos.position_level,
            'monthly_salary': pos.monthly_salary,
            'daily_salary': pos.daily_salary,
            'created_at': pos.created_at.isoformat(),
            'updated_at': pos.updated_at.isoformat()
        } for pos in positions]
    })

@app.route('/api/positions/by_city/<city_name>', methods=['GET'])
def get_positions_by_city(city_name):
    """根据城市获取岗位"""
    positions = PositionCost.query.filter_by(city_name=city_name).all()
    return jsonify([{
        'id': pos.id,
        'position_name': pos.position_name,
        'city_name': pos.city_name,
        'monthly_salary': pos.monthly_salary,
        'daily_salary': pos.daily_salary,
        'created_at': pos.created_at.isoformat(),
        'updated_at': pos.updated_at.isoformat()
    } for pos in positions])

@app.route('/api/quotation_records', methods=['GET'])
def get_quotation_records():
    """获取所有报价记录"""
    records = QuotationRecord.query.order_by(QuotationRecord.created_at.desc()).all()
    return jsonify([{
        'id': record.id,
        'opportunity_name': record.opportunity_name,
        'selected_cities': record.selected_cities,
        'selected_positions': record.selected_positions,
        'work_duration_months': record.work_duration_months,
        'work_duration_days': record.work_duration_days,
        'calculation_type': record.calculation_type,
        'quotation_coefficient': float(record.quotation_coefficient) if record.quotation_coefficient else None,
        'total_amount': record.total_amount,
        'created_at': record.created_at.isoformat()
    } for record in records])

@app.route('/api/opportunities', methods=['POST'])
def add_opportunity():
    """添加商机"""
    data = request.get_json()
    name = data.get('name', '').strip()
    description = data.get('description', '').strip()
    
    if not name:
        return jsonify({'success': False, 'message': '商机名称不能为空'})
    
    # 检查是否已存在
    existing = Opportunity.query.filter_by(name=name).first()
    if existing:
        return jsonify({'success': False, 'message': '商机名称已存在'})
    
    opportunity = Opportunity(name=name, description=description)
    db.session.add(opportunity)
    db.session.commit()
    
    return jsonify({'success': True, 'message': '商机添加成功'})

@app.route('/api/opportunities/<int:opportunity_id>', methods=['PUT'])
def update_opportunity(opportunity_id):
    """更新商机"""
    data = request.get_json()
    opportunity = Opportunity.query.get_or_404(opportunity_id)

    opportunity.name = data.get('name', opportunity.name).strip()
    opportunity.description = data.get('description', opportunity.description)

    db.session.commit()
    return jsonify({'success': True, 'message': '商机更新成功'})

@app.route('/api/opportunities/<int:opportunity_id>', methods=['DELETE'])
def delete_opportunity(opportunity_id):
    """删除商机"""
    opportunity = Opportunity.query.get_or_404(opportunity_id)
    db.session.delete(opportunity)
    db.session.commit()
    return jsonify({'success': True, 'message': '商机删除成功'})

@app.route('/cities')
def cities():
    """城市社保公积金管理页面"""
    cities = CitySocialInsurance.query.all()
    return render_template('cities.html', cities=cities)

@app.route('/api/cities', methods=['POST'])
def add_city():
    """添加城市社保公积金数据"""
    data = request.get_json()
    city_name = data.get('city_name', '').strip()
    social_base = data.get('social_base', 0)
    social_rate = data.get('social_rate', 0.16)  # 默认16%
    housing_base = data.get('housing_base', 0)
    housing_rate = data.get('housing_rate', 0.12)  # 默认12%

    if not city_name:
        return jsonify({'success': False, 'message': '城市名称不能为空'})

    try:
        social_base = float(social_base)
        social_rate = float(social_rate)
        housing_base = float(housing_base)
        housing_rate = float(housing_rate)
        
        # 自动计算公司缴纳总额
        company_amount = social_base * social_rate + housing_base * housing_rate
    except ValueError:
        return jsonify({'success': False, 'message': '数值必须为数字'})

    # 检查是否已存在
    existing = CitySocialInsurance.query.filter_by(city_name=city_name).first()
    if existing:
        # 更新现有记录
        existing.social_base = social_base
        existing.social_rate = social_rate
        existing.housing_base = housing_base
        existing.housing_rate = housing_rate
        existing.company_amount = company_amount
        existing.updated_at = datetime.utcnow()
    else:
        # 创建新记录
        city = CitySocialInsurance(
            city_name=city_name,
            social_base=social_base,
            social_rate=social_rate,
            housing_base=housing_base,
            housing_rate=housing_rate,
            company_amount=company_amount
        )
        db.session.add(city)

    db.session.commit()
    return jsonify({'success': True, 'message': '城市数据保存成功'})

@app.route('/api/cities/<int:city_id>', methods=['PUT'])
def update_city(city_id):
    """更新城市数据"""
    data = request.get_json()
    city = CitySocialInsurance.query.get_or_404(city_id)

    city.city_name = data.get('city_name', city.city_name).strip()
    city.social_base = float(data.get('social_base', city.social_base))
    city.social_rate = float(data.get('social_rate', city.social_rate))
    city.housing_base = float(data.get('housing_base', city.housing_base))
    city.housing_rate = float(data.get('housing_rate', city.housing_rate))
    
    # 自动计算公司缴纳总额
    city.company_amount = city.social_base * city.social_rate + city.housing_base * city.housing_rate
    city.updated_at = datetime.utcnow()

    db.session.commit()
    return jsonify({'success': True, 'message': '城市数据更新成功'})

@app.route('/api/cities/<int:city_id>', methods=['DELETE'])
def delete_city(city_id):
    """删除城市数据"""
    city = CitySocialInsurance.query.get_or_404(city_id)
    db.session.delete(city)
    db.session.commit()
    return jsonify({'success': True, 'message': '城市数据删除成功'})

@app.route('/positions')
def positions():
    """岗位成本管理页面"""
    positions = PositionCost.query.all()
    return render_template('positions.html', positions=positions)

@app.route('/api/positions', methods=['POST'])
def add_position():
    """添加岗位成本数据"""
    data = request.get_json()
    position_name = data.get('position_name', '').strip()
    city_name = data.get('city_name', '').strip()
    position_level = data.get('position_level', '').strip()
    monthly_salary = data.get('monthly_salary', 0)
    daily_salary = data.get('daily_salary', 0)

    if not position_name:
        return jsonify({'success': False, 'message': '岗位名称不能为空'})
    if not city_name:
        return jsonify({'success': False, 'message': '城市名称不能为空'})
    if not position_level:
        return jsonify({'success': False, 'message': '岗位级别不能为空'})

    try:
        monthly_salary = float(monthly_salary)
        daily_salary = float(daily_salary)
    except ValueError:
        return jsonify({'success': False, 'message': '工资必须为数字'})

    # 检查是否已存在（岗位+城市+级别的组合）
    existing = PositionCost.query.filter_by(position_name=position_name, city_name=city_name, position_level=position_level).first()
    if existing:
        # 更新现有记录
        existing.monthly_salary = monthly_salary
        existing.daily_salary = daily_salary
        existing.updated_at = datetime.utcnow()
    else:
        # 创建新记录
        position = PositionCost(
            position_name=position_name,
            city_name=city_name,
            position_level=position_level,
            monthly_salary=monthly_salary,
            daily_salary=daily_salary
        )
        db.session.add(position)

    db.session.commit()
    return jsonify({'success': True, 'message': '岗位数据保存成功'})

@app.route('/api/positions/<int:position_id>', methods=['PUT'])
def update_position(position_id):
    """更新岗位数据"""
    data = request.get_json()
    position = PositionCost.query.get_or_404(position_id)

    position.position_name = data.get('position_name', position.position_name).strip()
    position.city_name = data.get('city_name', position.city_name).strip()
    position.position_level = data.get('position_level', position.position_level).strip() if data.get('position_level') else position.position_level
    position.monthly_salary = float(data.get('monthly_salary', position.monthly_salary))
    position.daily_salary = float(data.get('daily_salary', position.daily_salary))
    position.updated_at = datetime.utcnow()

    db.session.commit()
    return jsonify({'success': True, 'message': '岗位数据更新成功'})

@app.route('/api/positions/<int:position_id>', methods=['DELETE'])
def delete_position(position_id):
    """删除岗位数据"""
    position = PositionCost.query.get_or_404(position_id)
    db.session.delete(position)
    db.session.commit()
    return jsonify({'success': True, 'message': '岗位数据删除成功'})

@app.route('/quotation')
def quotation():
    """报价生成页面"""
    opportunities = Opportunity.query.all()
    cities = CitySocialInsurance.query.all()
    positions = PositionCost.query.all()
    return render_template('quotation.html',
                         opportunities=opportunities,
                         cities=cities,
                         positions=positions)

@app.route('/api/generate_quotation', methods=['POST'])
def generate_quotation():
    """生成报价"""
    data = request.get_json()
    opportunity_name = data.get('opportunity_name', '').strip()
    selected_cities = data.get('selected_cities', [])
    selected_positions = data.get('selected_positions', [])
    calculation_type = data.get('calculation_type', 'monthly')  # monthly 或 daily
    work_duration_months = data.get('work_duration_months', 0)
    work_duration_days = data.get('work_duration_days', 0)
    quotation_coefficient = data.get('quotation_coefficient', 1.0)

    if not opportunity_name:
        return jsonify({'success': False, 'message': '请输入商机名称'})

    if not selected_cities:
        return jsonify({'success': False, 'message': '请选择至少一个城市'})

    if not selected_positions:
        return jsonify({'success': False, 'message': '请选择至少一个岗位'})

    # 验证工期参数
    if calculation_type == 'monthly':
        try:
            work_duration_months = int(work_duration_months)
            if work_duration_months <= 0:
                return jsonify({'success': False, 'message': '工期（月）必须大于0'})
        except ValueError:
            return jsonify({'success': False, 'message': '工期（月）必须为整数'})
    else:  # daily
        try:
            work_duration_days = int(work_duration_days)
            quotation_coefficient = round(float(quotation_coefficient), 2)  # 保留两位小数
            if work_duration_days <= 0:
                return jsonify({'success': False, 'message': '工期（天）必须大于0'})
            if quotation_coefficient <= 0:
                return jsonify({'success': False, 'message': '报价系数必须大于0'})
        except ValueError:
            return jsonify({'success': False, 'message': '工期和报价系数必须为数字'})

    # 获取所有需要的城市数据（包括岗位所在的城市）
    all_cities_needed = set(selected_cities)

    # 从岗位规格中提取城市
    for position_spec in selected_positions:
        if '@' in position_spec:
            parts = position_spec.split('@')
            if len(parts) >= 2:
                pos_city_name = parts[1]
                all_cities_needed.add(pos_city_name)

    cities_data = {city.city_name: city for city in CitySocialInsurance.query.filter(CitySocialInsurance.city_name.in_(list(all_cities_needed))).all()}

    # 检查城市数据完整性
    missing_cities = [city for city in all_cities_needed if city not in cities_data]
    if missing_cities:
        return jsonify({'success': False, 'message': f'缺少城市数据: {", ".join(missing_cities)}'})

    # 生成报价明细
    quotation_details = []
    total_amount = 0

    for position_spec in selected_positions:
        # 解析岗位规格：position_name@city_name@count
        count = 1
        if '@' in position_spec:
            parts = position_spec.split('@')
            if len(parts) == 3:
                position_name, pos_city_name, count_str = parts
                try:
                    count = max(1, int(count_str))
                except Exception:
                    count = 1
            else:
                position_name, pos_city_name = parts[0], parts[1]
        else:
            # 兼容旧格式，假设岗位适用于所有城市
            position_name = position_spec
            pos_city_name = None

        # 检查岗位城市数据是否存在
        if pos_city_name not in cities_data:
            return jsonify({'success': False, 'message': f'城市 {pos_city_name} 的数据不存在'})
        city_data = cities_data[pos_city_name]
        position_data = PositionCost.query.filter_by(
            position_name=position_name,
            city_name=pos_city_name
        ).first()
        if not position_data:
            return jsonify({'success': False, 'message': f'岗位 {position_name} 在城市 {pos_city_name} 的数据不存在'})

        if calculation_type == 'monthly':
            base_cost = position_data.monthly_salary * work_duration_months + city_data.company_amount * work_duration_months
            subtotal = base_cost * count
            detail = {
                'city_name': pos_city_name,
                'position_name': position_name,
                'position_level': position_data.position_level,
                'monthly_salary': position_data.monthly_salary,
                'daily_salary': None,
                'social_base': city_data.social_base,
                'company_insurance': city_data.company_amount,
                'work_duration_months': work_duration_months,
                'work_duration_days': None,
                'calculation_type': 'monthly',
                'quotation_coefficient': quotation_coefficient,
                'base_cost': base_cost,
                'subtotal': subtotal,
                'count': count
            }
        else:
            import math
            rounded_months = round(work_duration_days / 30)
            base_cost = position_data.daily_salary * work_duration_days + city_data.company_amount * rounded_months
            subtotal = base_cost * count
            detail = {
                'city_name': pos_city_name,
                'position_name': position_name,
                'position_level': position_data.position_level,
                'monthly_salary': None,
                'daily_salary': position_data.daily_salary,
                'social_base': city_data.social_base,
                'company_insurance': city_data.company_amount,
                'work_duration_months': None,
                'work_duration_days': work_duration_days,
                'calculation_type': 'daily',
                'quotation_coefficient': quotation_coefficient,
                'base_cost': base_cost,
                'subtotal': subtotal,
                'count': count
            }
        total_amount += subtotal
        quotation_details.append(detail)

    # 保存报价记录
    quotation_record = QuotationRecord(
        opportunity_name=opportunity_name,
        selected_cities=json.dumps(selected_cities, ensure_ascii=False),
        selected_positions=json.dumps(selected_positions, ensure_ascii=False),
        work_duration_months=work_duration_months if calculation_type == 'monthly' else None,
        work_duration_days=work_duration_days if calculation_type == 'daily' else None,
        calculation_type=calculation_type,
        quotation_coefficient=quotation_coefficient,
        total_amount=total_amount
    )
    db.session.add(quotation_record)
    db.session.flush()  # 获取ID

    # 保存报价明细
    for detail in quotation_details:
        quotation_detail = QuotationDetail(
            quotation_id=quotation_record.id,
            city_name=detail['city_name'],
            position_name=detail['position_name'],
            position_level=detail['position_level'],
            monthly_salary=detail['monthly_salary'],
            daily_salary=detail['daily_salary'],
            social_base=detail['social_base'],
            company_insurance=detail['company_insurance'],
            work_duration_months=detail['work_duration_months'],
            work_duration_days=detail['work_duration_days'],
            calculation_type=detail['calculation_type'],
            quotation_coefficient=detail['quotation_coefficient'],
            subtotal=detail['subtotal']
        )
        db.session.add(quotation_detail)

    db.session.commit()

    return jsonify({
        'success': True,
        'message': '报价生成成功',
        'quotation_id': quotation_record.id,
        'details': quotation_details,
        'total_amount': total_amount,
        'opportunity_name': opportunity_name,
        'calculation_type': calculation_type,
        'work_duration_months': work_duration_months if calculation_type == 'monthly' else None,
        'work_duration_days': work_duration_days if calculation_type == 'daily' else None
    })

@app.route('/quotation_records')
def quotation_records():
    """报价记录页面"""
    records = QuotationRecord.query.order_by(QuotationRecord.created_at.desc()).all()
    return render_template('quotation_records.html', records=records)

@app.route('/api/quotation_detail/<int:quotation_id>')
def get_quotation_detail(quotation_id):
    """获取报价详情"""
    record = QuotationRecord.query.get_or_404(quotation_id)
    details = QuotationDetail.query.filter_by(quotation_id=quotation_id).all()

    detail_list = []
    for detail in details:
        detail_list.append({
            'city_name': detail.city_name,
            'position_name': detail.position_name,
            'position_level': detail.position_level,
            'count': getattr(detail, 'count', 1),  # 默认为1
            'monthly_salary': detail.monthly_salary,
            'daily_salary': detail.daily_salary,
            'social_base': detail.social_base,
            'company_insurance': detail.company_insurance,
            'work_duration_months': detail.work_duration_months,
            'work_duration_days': detail.work_duration_days,
            'calculation_type': detail.calculation_type,
            'quotation_coefficient': float(detail.quotation_coefficient) if detail.quotation_coefficient else None,
            'subtotal': detail.subtotal,
            'external_price': getattr(detail, 'external_price', detail.subtotal)  # 默认为成本价
        })

    return jsonify({
        'record': {
            'id': record.id,
            'opportunity_name': record.opportunity_name,
            'selected_cities': json.loads(record.selected_cities),
            'selected_positions': json.loads(record.selected_positions),
            'work_duration_months': record.work_duration_months,
            'work_duration_days': record.work_duration_days,
            'calculation_type': record.calculation_type,
            'quotation_coefficient': float(record.quotation_coefficient) if record.quotation_coefficient else None,
            'total_amount': record.total_amount,
            'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
        },
        'details': detail_list
    })

@app.route('/api/export_quotation/<int:quotation_id>')
def export_quotation(quotation_id):
    """导出单个报价为Excel"""
    record = QuotationRecord.query.get_or_404(quotation_id)
    details = QuotationDetail.query.filter_by(quotation_id=quotation_id).all()

    # 创建Excel数据
    data = []
    city_totals = {}  # 用于城市汇总

    for detail in details:
        # 根据计算类型显示不同的工资列
        if detail.calculation_type == 'monthly':
            salary_column = '月工资'
            salary_value = detail.monthly_salary
            duration_column = '工期(月)'
            duration_value = detail.work_duration_months
        else:
            salary_column = '日工资'
            salary_value = detail.daily_salary
            duration_column = '工期(天)'
            duration_value = detail.work_duration_days

        row_data = {
            '城市': detail.city_name,
            '岗位': detail.position_name,
            salary_column: salary_value,
            '社保基数': detail.social_base,
            '公司社保公积金': detail.company_insurance,
            '报价系数': float(detail.quotation_coefficient) if detail.quotation_coefficient else 1.0,
            duration_column: duration_value,
            '小计': detail.subtotal
        }
        data.append(row_data)

        # 计算城市汇总
        city_name = detail.city_name
        if city_name not in city_totals:
            city_totals[city_name] = 0
        city_totals[city_name] += detail.subtotal

    df = pd.DataFrame(data)

    # 创建Excel文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='报价明细', index=False)

        # 添加城市汇总表
        city_summary_data = []
        for city_name, total in city_totals.items():
            city_summary_data.append({
                '城市': city_name,
                '小计': total
            })
        city_df = pd.DataFrame(city_summary_data)
        city_df.to_excel(writer, sheet_name='城市汇总', index=False)

        # 添加报价汇总信息
        summary_data = {
            '商机名称': [record.opportunity_name],
            '计算类型': ['按月计算' if record.calculation_type == 'monthly' else '按天计算'],
            '报价系数': [float(record.quotation_coefficient) if record.quotation_coefficient else 1.0],
            '总金额': [record.total_amount],
            '生成时间': [record.created_at.strftime('%Y-%m-%d %H:%M:%S')]
        }

        if record.calculation_type == 'monthly':
            summary_data['工期(月)'] = [record.work_duration_months]
        else:
            summary_data['工期(天)'] = [record.work_duration_days]

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='报价汇总', index=False)

    output.seek(0)

    filename = f"报价_{record.opportunity_name}_{record.created_at.strftime('%Y%m%d_%H%M%S')}.xlsx"

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )

@app.route('/api/export_all_quotations')
def export_all_quotations():
    """导出所有报价记录为Excel"""
    records = QuotationRecord.query.order_by(QuotationRecord.created_at.desc()).all()

    # 创建汇总数据
    summary_data = []
    for record in records:
        row_data = {
            'ID': record.id,
            '商机名称': record.opportunity_name,
            '选择城市': ', '.join(json.loads(record.selected_cities)),
            '选择岗位': ', '.join(json.loads(record.selected_positions)),
            '计算类型': '按月计算' if record.calculation_type == 'monthly' else '按天计算',
            '报价系数': float(record.quotation_coefficient) if record.quotation_coefficient else 1.0,
            '总金额': record.total_amount,
            '生成时间': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

        if record.calculation_type == 'monthly':
            row_data['工期(月)'] = record.work_duration_months
        else:
            row_data['工期(天)'] = record.work_duration_days

        summary_data.append(row_data)

    df = pd.DataFrame(summary_data)

    # 创建Excel文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='报价记录汇总', index=False)

        # 为每个报价添加详细明细表
        for record in records[:10]:  # 限制前10个记录，避免文件过大
            details = QuotationDetail.query.filter_by(quotation_id=record.id).all()
            detail_data = []
            for detail in details:
                # 根据计算类型显示不同的工资列
                if detail.calculation_type == 'monthly':
                    salary_column = '月工资'
                    salary_value = detail.monthly_salary
                    duration_column = '工期(月)'
                    duration_value = detail.work_duration_months
                else:
                    salary_column = '日工资'
                    salary_value = detail.daily_salary
                    duration_column = '工期(天)'
                    duration_value = detail.work_duration_days

                row_data = {
                    '城市': detail.city_name,
                    '岗位': detail.position_name,
                    salary_column: salary_value,
                    '社保基数': detail.social_base,
                    '公司社保公积金': detail.company_insurance,
                    '报价系数': float(detail.quotation_coefficient) if detail.quotation_coefficient else 1.0,
                    duration_column: duration_value,
                    '小计': detail.subtotal
                }
                detail_data.append(row_data)

            if detail_data:
                detail_df = pd.DataFrame(detail_data)
                sheet_name = f"报价{record.id}_{record.opportunity_name[:10]}"
                detail_df.to_excel(writer, sheet_name=sheet_name, index=False)

    output.seek(0)

    filename = f"所有报价记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )

@app.route('/api/import_cities', methods=['POST'])
def import_cities():
    """导入城市社保公积金数据"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '请选择文件'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '请选择文件'})

    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'success': False, 'message': '请上传Excel文件'})

    try:
        df = pd.read_excel(file)

        # 检查必需的列
        required_columns = ['城市名称', '社保基数', '社保缴纳比例', '公积金基数', '公积金缴纳比例']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            return jsonify({'success': False, 'message': f'缺少必需的列: {", ".join(missing_columns)}'})

        # 导入数据
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            city_name = str(row['城市名称']).strip()
            social_base = float(row['社保基数'])
            social_rate = float(row['社保缴纳比例']) / 100  # 转换为小数
            housing_base = float(row['公积金基数'])
            housing_rate = float(row['公积金缴纳比例']) / 100  # 转换为小数
            
            # 自动计算公司缴纳总额
            company_amount = social_base * social_rate + housing_base * housing_rate

            if not city_name:
                continue

            existing = CitySocialInsurance.query.filter_by(city_name=city_name).first()
            if existing:
                existing.social_base = social_base
                existing.social_rate = social_rate
                existing.housing_base = housing_base
                existing.housing_rate = housing_rate
                existing.company_amount = company_amount
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                city = CitySocialInsurance(
                    city_name=city_name,
                    social_base=social_base,
                    social_rate=social_rate,
                    housing_base=housing_base,
                    housing_rate=housing_rate,
                    company_amount=company_amount
                )
                db.session.add(city)
                imported_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'导入成功！新增 {imported_count} 条，更新 {updated_count} 条记录'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'导入失败: {str(e)}'})

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, port=5001)
