{% extends "base.html" %}

{% block title %}报价记录 - 商机报价系统{% endblock %}

{% block content %}
<div id="alerts"></div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">报价记录</h5>
                <button type="button" class="btn btn-success" onclick="exportAllQuotations()">
                    <i class="bi bi-download"></i> 导出所有记录
                </button>
            </div>
            <div class="card-body">
                {% if records %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>商机名称</th>
                                <th>选择城市</th>
                                <th>选择岗位</th>
                                <th>工期(月)</th>
                                <th>总金额</th>
                                <th>生成时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in records %}
                            <tr>
                                <td>{{ record.id }}</td>
                                <td>{{ record.opportunity_name }}</td>
                                <td>
                                    {% set cities = record.selected_cities|from_json %}
                                    <span class="badge bg-info">{{ cities|length }}个城市</span>
                                    <small class="text-muted d-block">{{ cities|join(', ') }}</small>
                                </td>
                                <td>
                                    {% set positions = record.selected_positions|from_json %}
                                    <span class="badge bg-warning">{{ positions|length }}个岗位</span>
                                    <small class="text-muted d-block">{{ positions|join(', ') }}</small>
                                </td>
                                <td>{{ record.work_duration_months }}</td>
                                <td class="fw-bold">¥{{ "%.2f"|format(record.total_amount) }}</td>
                                <td>{{ record.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td>
                                    <div class="btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewDetail({{ record.id }})">
                                            <i class="bi bi-eye"></i> 查看
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="exportSingle({{ record.id }})">
                                            <i class="bi bi-download"></i> 导出
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-list-ul display-4 text-muted"></i>
                    <p class="text-muted mt-2">暂无报价记录</p>
                    <a href="{{ url_for('quotation') }}" class="btn btn-primary">
                        生成第一个报价
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 报价详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">报价详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="detailContent">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" id="exportDetailBtn" onclick="exportFromDetail()">
                    <i class="bi bi-download"></i> 导出Excel
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentDetailId = null;

function viewDetail(quotationId) {
    currentDetailId = quotationId;
    $('#detailModal').modal('show');
    
    // 重置内容
    $('#detailContent').html(`
        <div class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `);
    
    $.ajax({
        url: `/api/quotation_detail/${quotationId}`,
        method: 'GET',
        success: function(response) {
            displayDetailContent(response);
        },
        error: function() {
            $('#detailContent').html(`
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 加载详情失败
                </div>
            `);
        }
    });
}

function displayDetailContent(data) {
    const record = data.record;
    const details = data.details;
    
    let html = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>报价ID：</strong></td><td>${record.id}</td></tr>
                    <tr><td><strong>商机名称：</strong></td><td>${record.opportunity_name}</td></tr>
                    <tr><td><strong>工期：</strong></td><td>${record.work_duration_months}个月</td></tr>
                    <tr><td><strong>生成时间：</strong></td><td>${record.created_at}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>选择范围</h6>
                <table class="table table-sm">
                    <tr><td><strong>选择城市：</strong></td><td>${record.selected_cities.join(', ')}</td></tr>
                    <tr><td><strong>选择岗位：</strong></td><td>${record.selected_positions.join(', ')}</td></tr>
                    <tr><td><strong>总金额：</strong></td><td class="fw-bold text-primary">¥${record.total_amount.toFixed(2)}</td></tr>
                </table>
            </div>
        </div>
        
        <h6>报价明细</h6>
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>城市</th>
                        <th>岗位</th>
                        <th>月工资</th>
                        <th>个人社保公积金</th>
                        <th>公司社保公积金</th>
                        <th>月度总计</th>
                        <th>工期小计</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    details.forEach(function(detail) {
        const monthlyTotal = detail.monthly_salary + detail.personal_insurance + detail.company_insurance;
        html += `
            <tr>
                <td>${detail.city_name}</td>
                <td>${detail.position_name}</td>
                <td>¥${detail.monthly_salary.toFixed(2)}</td>
                <td>¥${detail.personal_insurance.toFixed(2)}</td>
                <td>¥${detail.company_insurance.toFixed(2)}</td>
                <td>¥${monthlyTotal.toFixed(2)}</td>
                <td class="fw-bold">¥${detail.subtotal.toFixed(2)}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="6" class="text-end">总计：</th>
                        <th>¥${record.total_amount.toFixed(2)}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;
    
    $('#detailContent').html(html);
}

function exportSingle(quotationId) {
    window.open(`/api/export_quotation/${quotationId}`, '_blank');
}

function exportFromDetail() {
    if (currentDetailId) {
        exportSingle(currentDetailId);
    }
}

function exportAllQuotations() {
    window.open('/api/export_all_quotations', '_blank');
}
</script>

<!-- 添加自定义过滤器 -->
<script>
// 为了支持模板中的 from_json 过滤器，我们需要在后端添加
</script>
{% endblock %}
