{% extends "base.html" %}

{% block title %}生成报价 - 商机报价系统{% endblock %}

{% block content %}
<div id="alerts"></div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">报价参数设置</h5>
            </div>
            <div class="card-body">
                <form id="quotationForm">
                    <div class="mb-3">
                        <label for="opportunityName" class="form-label">商机名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="opportunityName" list="opportunityList" required>
                        <datalist id="opportunityList">
                            {% for opportunity in opportunities %}
                            <option value="{{ opportunity.name }}">
                            {% endfor %}
                        </datalist>
                    </div>
                    
                    <div class="mb-3">
                        <label for="workDuration" class="form-label">工期(月) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="workDuration" min="1" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">选择城市 <span class="text-danger">*</span></label>
                        <div class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                            {% for city in cities %}
                            <div class="form-check">
                                <input class="form-check-input city-checkbox" type="checkbox" value="{{ city.city_name }}" id="city{{ loop.index }}">
                                <label class="form-check-label" for="city{{ loop.index }}">
                                    {{ city.city_name }} (个人:¥{{ "%.2f"|format(city.personal_amount) }}, 公司:¥{{ "%.2f"|format(city.company_amount) }})
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        {% if not cities %}
                        <small class="text-muted">请先在<a href="{{ url_for('cities') }}">城市管理</a>中添加城市数据</small>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">选择岗位 <span class="text-danger">*</span></label>
                        <div class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                            {% for position in positions %}
                            <div class="form-check">
                                <input class="form-check-input position-checkbox" type="checkbox" value="{{ position.position_name }}" id="position{{ loop.index }}">
                                <label class="form-check-label" for="position{{ loop.index }}">
                                    {{ position.position_name }} (¥{{ "%.2f"|format(position.monthly_salary) }}/月)
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        {% if not positions %}
                        <small class="text-muted">请先在<a href="{{ url_for('positions') }}">岗位管理</a>中添加岗位数据</small>
                        {% endif %}
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-calculator"></i> 生成报价
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">报价明细表</h5>
                <div id="exportButtons" style="display: none;">
                    <button type="button" class="btn btn-success btn-sm" onclick="exportQuotation()">
                        <i class="bi bi-download"></i> 导出Excel
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="quotationResult" class="text-center text-muted py-5">
                    <i class="bi bi-file-earmark-text display-4"></i>
                    <p class="mt-2">请设置报价参数并点击"生成报价"</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentQuotationId = null;

$('#quotationForm').on('submit', function(e) {
    e.preventDefault();
    generateQuotation();
});

function generateQuotation() {
    const opportunityName = $('#opportunityName').val().trim();
    const workDuration = $('#workDuration').val();
    const selectedCities = $('.city-checkbox:checked').map(function() {
        return this.value;
    }).get();
    const selectedPositions = $('.position-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (!opportunityName) {
        showAlert('请输入商机名称', 'danger');
        return;
    }
    
    if (!workDuration || workDuration <= 0) {
        showAlert('请输入有效的工期', 'danger');
        return;
    }
    
    if (selectedCities.length === 0) {
        showAlert('请至少选择一个城市', 'danger');
        return;
    }
    
    if (selectedPositions.length === 0) {
        showAlert('请至少选择一个岗位', 'danger');
        return;
    }
    
    const data = {
        opportunity_name: opportunityName,
        work_duration_months: parseInt(workDuration),
        selected_cities: selectedCities,
        selected_positions: selectedPositions
    };
    
    // 显示加载状态
    $('#quotationResult').html(`
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">生成中...</span>
            </div>
            <p class="mt-2">正在生成报价明细...</p>
        </div>
    `);
    
    $.ajax({
        url: '/api/generate_quotation',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                currentQuotationId = response.quotation_id;
                displayQuotationResult(response);
                showAlert(response.message, 'success');
                $('#exportButtons').show();
            } else {
                showAlert(response.message, 'danger');
                $('#quotationResult').html(`
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-exclamation-triangle display-4"></i>
                        <p class="mt-2">报价生成失败</p>
                    </div>
                `);
            }
        },
        error: function() {
            showAlert('报价生成失败，请稍后重试', 'danger');
            $('#quotationResult').html(`
                <div class="text-center text-muted py-5">
                    <i class="bi bi-exclamation-triangle display-4"></i>
                    <p class="mt-2">报价生成失败</p>
                </div>
            `);
        }
    });
}

function displayQuotationResult(response) {
    let html = `
        <div class="mb-3">
            <h6>商机名称：${response.details[0] ? $('#opportunityName').val() : ''}</h6>
            <h6>工期：${response.details[0] ? response.details[0].work_duration_months : ''}个月</h6>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>城市</th>
                        <th>岗位</th>
                        <th>月工资</th>
                        <th>个人社保公积金</th>
                        <th>公司社保公积金</th>
                        <th>月度总计</th>
                        <th>工期小计</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    response.details.forEach(function(detail) {
        html += `
            <tr>
                <td>${detail.city_name}</td>
                <td>${detail.position_name}</td>
                <td>¥${detail.monthly_salary.toFixed(2)}</td>
                <td>¥${detail.personal_insurance.toFixed(2)}</td>
                <td>¥${detail.company_insurance.toFixed(2)}</td>
                <td>¥${detail.monthly_total.toFixed(2)}</td>
                <td class="fw-bold">¥${detail.subtotal.toFixed(2)}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="6" class="text-end">总计：</th>
                        <th>¥${response.total_amount.toFixed(2)}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;
    
    $('#quotationResult').html(html);
}

function exportQuotation() {
    if (!currentQuotationId) {
        showAlert('请先生成报价', 'danger');
        return;
    }
    
    window.open(`/api/export_quotation/${currentQuotationId}`, '_blank');
}
</script>
{% endblock %}
