#!/usr/bin/env python3
"""
创建示例数据的脚本
"""
import pandas as pd

def create_sample_excel():
    """创建示例Excel文件"""
    # 城市社保公积金示例数据
    city_data = [
        {'城市名称': '北京', '个人缴纳金额': 1200.50, '公司缴纳金额': 2800.75},
        {'城市名称': '上海', '个人缴纳金额': 1150.30, '公司缴纳金额': 2650.80},
        {'城市名称': '广州', '个人缴纳金额': 980.20, '公司缴纳金额': 2200.45},
        {'城市名称': '深圳', '个人缴纳金额': 1100.60, '公司缴纳金额': 2500.90},
        {'城市名称': '杭州', '个人缴纳金额': 950.40, '公司缴纳金额': 2100.30},
        {'城市名称': '南京', '个人缴纳金额': 900.80, '公司缴纳金额': 2000.60},
        {'城市名称': '武汉', '个人缴纳金额': 850.70, '公司缴纳金额': 1900.40},
        {'城市名称': '成都', '个人缴纳金额': 800.90, '公司缴纳金额': 1800.20},
    ]
    
    df = pd.DataFrame(city_data)
    df.to_excel('城市社保公积金导入模板.xlsx', index=False, sheet_name='城市数据')
    print("已创建城市社保公积金导入模板.xlsx")

def create_sample_database():
    """创建示例数据库数据"""
    from app import app, db, Opportunity, CitySocialInsurance, PositionCost
    
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 添加示例商机
        opportunities = [
            {'name': '某大型企业人力资源外包项目', 'description': '为某大型制造企业提供全国多地人力资源外包服务'},
            {'name': '互联网公司技术团队外包', 'description': '为互联网公司提供技术开发团队外包服务'},
            {'name': '金融机构客服中心项目', 'description': '为银行提供客服中心人员外包服务'},
        ]
        
        for opp_data in opportunities:
            existing = Opportunity.query.filter_by(name=opp_data['name']).first()
            if not existing:
                opportunity = Opportunity(**opp_data)
                db.session.add(opportunity)
        
        # 添加示例城市数据
        cities = [
            {'city_name': '北京', 'personal_amount': 1200.50, 'company_amount': 2800.75},
            {'city_name': '上海', 'personal_amount': 1150.30, 'company_amount': 2650.80},
            {'city_name': '广州', 'personal_amount': 980.20, 'company_amount': 2200.45},
            {'city_name': '深圳', 'personal_amount': 1100.60, 'company_amount': 2500.90},
            {'city_name': '杭州', 'personal_amount': 950.40, 'company_amount': 2100.30},
        ]
        
        for city_data in cities:
            existing = CitySocialInsurance.query.filter_by(city_name=city_data['city_name']).first()
            if not existing:
                city = CitySocialInsurance(**city_data)
                db.session.add(city)
        
        # 添加示例岗位数据
        positions = [
            {'position_name': '软件开发工程师', 'monthly_salary': 15000.00, 'daily_salary': 682.00},
            {'position_name': '项目经理', 'monthly_salary': 20000.00, 'daily_salary': 909.00},
            {'position_name': '测试工程师', 'monthly_salary': 12000.00, 'daily_salary': 545.00},
            {'position_name': '产品经理', 'monthly_salary': 18000.00, 'daily_salary': 818.00},
            {'position_name': 'UI设计师', 'monthly_salary': 13000.00, 'daily_salary': 591.00},
            {'position_name': '运维工程师', 'monthly_salary': 14000.00, 'daily_salary': 636.00},
            {'position_name': '客服专员', 'monthly_salary': 8000.00, 'daily_salary': 364.00},
            {'position_name': '销售代表', 'monthly_salary': 10000.00, 'daily_salary': 455.00},
        ]
        
        for pos_data in positions:
            existing = PositionCost.query.filter_by(position_name=pos_data['position_name']).first()
            if not existing:
                position = PositionCost(**pos_data)
                db.session.add(position)
        
        db.session.commit()
        print("示例数据已添加到数据库")

if __name__ == '__main__':
    create_sample_excel()
    create_sample_database()
